{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Synthetic I-Section Pile Data Generation for PointNet++\n", "\n", "This notebook generates synthetic training data for I-section pile detection using PointNet++. It creates realistic I-section pile point clouds with proper geometric characteristics, noise simulation, and environmental context.\n", "\n", "**Purpose**: Create training dataset for PointNet++ pile detection\n", "**Input**: Pile geometry parameters and generation settings\n", "**Output**: Synthetic point clouds with labels for training\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Parameters (Papermill)\n", "num_positive_samples = 1000  # I-section pile samples\n", "num_negative_samples = 2000  # Non-pile samples\n", "patch_size = 2.0  # meters\n", "points_per_patch = 1024  # Standard for PointNet++\n", "noise_level = 0.02  # 2cm noise (typical drone LiDAR)\n", "output_dir = \"../../data/synthetic/pile_training\"\n", "save_visualizations = True\n", "random_seed = 42"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Imports\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import open3d as o3d\n", "import torch\n", "from pathlib import Path\n", "import json\n", "from datetime import datetime\n", "from scipy.spatial.transform import Rotation\n", "import pandas as pd\n", "from tqdm import tqdm\n", "\n", "# Set random seeds for reproducibility\n", "np.random.seed(random_seed)\n", "torch.manual_seed(random_seed)\n", "\n", "# Create output directory\n", "output_path = Path(output_dir)\n", "output_path.mkdir(parents=True, exist_ok=True)\n", "\n", "print(f\"🔧 SYNTHETIC I-SECTION PILE DATA GENERATION\")\n", "print(f\"Positive samples: {num_positive_samples}\")\n", "print(f\"Negative samples: {num_negative_samples}\")\n", "print(f\"Patch size: {patch_size}m\")\n", "print(f\"Points per patch: {points_per_patch}\")\n", "print(f\"Noise level: {noise_level}m\")\n", "print(f\"Output: {output_path}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## I-Section Pile Geometry Generation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class ISectionPileGenerator:\n", "    \"\"\"Generate synthetic I-section pile point clouds with realistic characteristics.\"\"\"\n", "    \n", "    def __init__(self):\n", "        # I-section pile parameters (based on typical solar foundation piles)\n", "        self.flange_width_range = (0.15, 0.25)      # 15-25cm flange width\n", "        self.flange_thickness_range = (0.01, 0.02)  # 1-2cm flange thickness\n", "        self.web_height_range = (0.20, 0.40)        # 20-40cm web height\n", "        self.web_thickness_range = (0.008, 0.015)   # 8-15mm web thickness\n", "        self.pile_length_range = (2.0, 8.0)         # 2-8m above-ground height\n", "        \n", "        # Point density parameters\n", "        self.points_per_cm2 = 2.0  # Typical drone LiDAR density\n", "        \n", "    def generate_i_section_geometry(self, params=None):\n", "        \"\"\"Generate I-section pile geometry parameters.\"\"\"\n", "        if params is None:\n", "            params = {\n", "                'flange_width': np.random.uniform(*self.flange_width_range),\n", "                'flange_thickness': np.random.uniform(*self.flange_thickness_range),\n", "                'web_height': np.random.uniform(*self.web_height_range),\n", "                'web_thickness': np.random.uniform(*self.web_thickness_range),\n", "                'pile_length': np.random.uniform(*self.pile_length_range)\n", "            }\n", "        \n", "        return params\n", "    \n", "    def create_i_section_points(self, params):\n", "        \"\"\"Create point cloud for I-section pile geometry.\"\"\"\n", "        points = []\n", "        \n", "        # Extract parameters\n", "        fw = params['flange_width']\n", "        ft = params['flange_thickness']\n", "        wh = params['web_height']\n", "        wt = params['web_thickness']\n", "        pl = params['pile_length']\n", "        \n", "        # Generate points for top flange\n", "        top_flange_points = self._generate_flange_points(\n", "            width=fw, thickness=ft, z_center=wh/2, length=pl\n", "        )\n", "        points.extend(top_flange_points)\n", "        \n", "        # Generate points for bottom flange\n", "        bottom_flange_points = self._generate_flange_points(\n", "            width=fw, thickness=ft, z_center=-wh/2, length=pl\n", "        )\n", "        points.extend(bottom_flange_points)\n", "        \n", "        # Generate points for web\n", "        web_points = self._generate_web_points(\n", "            height=wh, thickness=wt, length=pl\n", "        )\n", "        points.extend(web_points)\n", "        \n", "        return np.array(points)\n", "    \n", "    def _generate_flange_points(self, width, thickness, z_center, length):\n", "        \"\"\"Generate points for flange (horizontal part of I-section).\"\"\"\n", "        points = []\n", "        \n", "        # Calculate point spacing based on density\n", "        area = width * thickness\n", "        num_points_xy = int(area * self.points_per_cm2 * 10000)  # Convert m² to cm²\n", "        num_points_z = max(1, int(length * self.points_per_cm2 * 100))  # Convert m to cm\n", "        \n", "        # Generate points in flange volume\n", "        for _ in range(num_points_xy * num_points_z):\n", "            x = np.random.uniform(-width/2, width/2)\n", "            y = np.random.uniform(0, length)\n", "            z = np.random.uniform(z_center - thickness/2, z_center + thickness/2)\n", "            points.append([x, y, z])\n", "        \n", "        return points\n", "    \n", "    def _generate_web_points(self, height, thickness, length):\n", "        \"\"\"Generate points for web (vertical part of I-section).\"\"\"\n", "        points = []\n", "        \n", "        # Calculate point spacing\n", "        area = height * thickness\n", "        num_points_xz = int(area * self.points_per_cm2 * 10000)\n", "        num_points_y = max(1, int(length * self.points_per_cm2 * 100))\n", "        \n", "        # Generate points in web volume\n", "        for _ in range(num_points_xz * num_points_y):\n", "            x = np.random.uniform(-thickness/2, thickness/2)\n", "            y = np.random.uniform(0, length)\n", "            z = np.random.uniform(-height/2, height/2)\n", "            points.append([x, y, z])\n", "        \n", "        return points\n", "    \n", "    def add_realistic_noise(self, points, noise_level):\n", "        \"\"\"Add realistic noise to simulate drone LiDAR characteristics.\"\"\"\n", "        # Gaussian noise for each coordinate\n", "        noise = np.random.normal(0, noise_level, points.shape)\n", "        \n", "        # Add slightly more noise in Z direction (typical for LiDAR)\n", "        noise[:, 2] *= 1.2\n", "        \n", "        return points + noise\n", "    \n", "    def apply_random_transformation(self, points):\n", "        \"\"\"Apply random rotation and translation to simulate installation variations.\"\"\"\n", "        # Random rotation around Y-axis (pile orientation)\n", "        rotation_angle = np.random.uniform(-15, 15)  # ±15° typical installation tolerance\n", "        rotation = Rotation.from_euler('y', rotation_angle, degrees=True)\n", "        \n", "        # Apply rotation\n", "        rotated_points = rotation.apply(points)\n", "        \n", "        # Random translation within patch\n", "        translation = np.random.uniform(-0.5, 0.5, 3)  # ±50cm within 2m patch\n", "        translated_points = rotated_points + translation\n", "        \n", "        return translated_points\n", "    \n", "    def generate_pile_sample(self, target_points=1024):\n", "        \"\"\"Generate a complete I-section pile sample.\"\"\"\n", "        # Generate geometry parameters\n", "        params = self.generate_i_section_geometry()\n", "        \n", "        # Create base geometry\n", "        points = self.create_i_section_points(params)\n", "        \n", "        # Apply transformations\n", "        points = self.apply_random_transformation(points)\n", "        \n", "        # Add noise\n", "        points = self.add_realistic_noise(points, noise_level)\n", "        \n", "        # Subsample or upsample to target number of points\n", "        if len(points) > target_points:\n", "            # Random subsampling\n", "            indices = np.random.choice(len(points), target_points, replace=False)\n", "            points = points[indices]\n", "        elif len(points) < target_points:\n", "            # Upsample by adding jittered copies\n", "            while len(points) < target_points:\n", "                # Add jittered copies of existing points\n", "                jitter = np.random.normal(0, noise_level/2, points.shape)\n", "                jittered_points = points + jitter\n", "                points = np.vstack([points, jittered_points])\n", "            \n", "            # Final subsampling to exact target\n", "            indices = np.random.choice(len(points), target_points, replace=False)\n", "            points = points[indices]\n", "        \n", "        # Create labels (1 for pile points)\n", "        labels = np.ones(len(points), dtype=int)\n", "        \n", "        return points, labels, params\n", "\n", "# Initialize generator\n", "pile_generator = ISectionPileGenerator()\n", "print(\"✅ I-Section pile generator initialized\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Negative Sample Generation"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class NegativeSampleGenerator:\n", "    \"\"\"Generate negative samples (non-pile structures) for training.\"\"\"\n", "    \n", "    def __init__(self):\n", "        self.sample_types = [\n", "            'ground_plane',\n", "            'vegetation',\n", "            'cylindrical_pole',\n", "            'rectangular_structure',\n", "            'random_noise'\n", "        ]\n", "    \n", "    def generate_ground_plane(self, target_points=1024):\n", "        \"\"\"Generate ground plane points.\"\"\"\n", "        # Create roughly planar ground with some variation\n", "        x = np.random.uniform(-1.0, 1.0, target_points)\n", "        y = np.random.uniform(-1.0, 1.0, target_points)\n", "        \n", "        # Add slight slope and roughness\n", "        slope_x = np.random.uniform(-0.1, 0.1)\n", "        slope_y = np.random.uniform(-0.1, 0.1)\n", "        roughness = np.random.normal(0, 0.05, target_points)\n", "        \n", "        z = slope_x * x + slope_y * y + roughness\n", "        \n", "        points = np.column_stack([x, y, z])\n", "        labels = np.zeros(len(points), dtype=int)\n", "        \n", "        return points, labels\n", "    \n", "    def generate_vegetation(self, target_points=1024):\n", "        \"\"\"Generate vegetation-like point clusters.\"\"\"\n", "        points = []\n", "        \n", "        # Create multiple vegetation clusters\n", "        num_clusters = np.random.randint(3, 8)\n", "        points_per_cluster = target_points // num_clusters\n", "        \n", "        for _ in range(num_clusters):\n", "            # Random cluster center\n", "            center = np.random.uniform(-0.8, 0.8, 3)\n", "            center[2] = np.random.uniform(0, 2.0)  # Above ground\n", "            \n", "            # Generate points around center with vegetation-like distribution\n", "            cluster_points = np.random.normal(center, [0.3, 0.3, 0.5], (points_per_cluster, 3))\n", "            points.extend(cluster_points)\n", "        \n", "        # Fill remaining points\n", "        remaining = target_points - len(points)\n", "        if remaining > 0:\n", "            extra_points = np.random.uniform(-1.0, 1.0, (remaining, 3))\n", "            extra_points[:, 2] = np.random.uniform(0, 3.0, remaining)\n", "            points.extend(extra_points)\n", "        \n", "        points = np.array(points[:target_points])\n", "        labels = np.zeros(len(points), dtype=int)\n", "        \n", "        return points, labels\n", "    \n", "    def generate_cylindrical_pole(self, target_points=1024):\n", "        \"\"\"Generate cylindrical pole (different from I-section).\"\"\"\n", "        # Cylinder parameters\n", "        radius = np.random.uniform(0.05, 0.15)  # 5-15cm radius\n", "        height = np.random.uniform(1.0, 4.0)    # 1-4m height\n", "        \n", "        # Generate cylindrical points\n", "        angles = np.random.uniform(0, 2*np.pi, target_points)\n", "        radii = np.random.uniform(0, radius, target_points)\n", "        heights = np.random.uniform(0, height, target_points)\n", "        \n", "        x = radii * np.cos(angles)\n", "        y = radii * np.sin(angles)\n", "        z = heights\n", "        \n", "        points = np.column_stack([x, y, z])\n", "        \n", "        # Add noise\n", "        points = pile_generator.add_realistic_noise(points, noise_level)\n", "        \n", "        labels = np.zeros(len(points), dtype=int)\n", "        \n", "        return points, labels\n", "    \n", "    def generate_negative_sample(self, target_points=1024):\n", "        \"\"\"Generate a random negative sample.\"\"\"\n", "        sample_type = np.random.choice(self.sample_types)\n", "        \n", "        if sample_type == 'ground_plane':\n", "            return self.generate_ground_plane(target_points)\n", "        elif sample_type == 'vegetation':\n", "            return self.generate_vegetation(target_points)\n", "        elif sample_type == 'cylindrical_pole':\n", "            return self.generate_cylindrical_pole(target_points)\n", "        else:\n", "            # Random noise as fallback\n", "            points = np.random.uniform(-1.0, 1.0, (target_points, 3))\n", "            labels = np.zeros(target_points, dtype=int)\n", "            return points, labels\n", "\n", "# Initialize negative sample generator\n", "negative_generator = NegativeSampleGenerator()\n", "print(\"✅ Negative sample generator initialized\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}