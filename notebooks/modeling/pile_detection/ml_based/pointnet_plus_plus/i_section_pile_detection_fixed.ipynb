# Parameters (Papermill)
site_name = "trino_enel"
ground_method = "csf"  # Options: csf, pmf, ransac
confidence_threshold = 0.7
output_dir = "../../data/output_runs/pile_detection"
enable_visualization = True
save_results = True

# Imports
import numpy as np
import open3d as o3d
import matplotlib.pyplot as plt
from pathlib import Path
import pandas as pd
from sklearn.cluster import DBSCAN
from sklearn.neighbors import NearestNeighbors
import json
from datetime import datetime

# Setup
output_path = Path(output_dir) / ground_method
output_path.mkdir(parents=True, exist_ok=True)

print(f"🔧 I-SECTION PILE DETECTION - {ground_method.upper()}")
print(f"Site: {site_name}")
print(f"Output: {output_path}")

# Load non-ground point cloud from ground segmentation
nonground_file = Path(f"../../data/processed/{site_name}/ground_segmentation/{ground_method}/{site_name}_nonground.ply")

if not nonground_file.exists():
    print(f"❌ File not found: {nonground_file}")
    # Try alternative naming
    alt_file = Path(f"../../data/processed/{site_name}/ground_segmentation/{ground_method}/trino_enel_nonground.ply")
    if alt_file.exists():
        nonground_file = alt_file
        print(f"✅ Using alternative file: {nonground_file}")
    else:
        raise FileNotFoundError(f"No non-ground point cloud found for {site_name} with {ground_method}")

# Load point cloud
pcd = o3d.io.read_point_cloud(str(nonground_file))
points = np.asarray(pcd.points)

print(f"✅ Loaded point cloud: {points.shape[0]:,} points")
print(f"Bounds: X[{points[:, 0].min():.1f}, {points[:, 0].max():.1f}] Y[{points[:, 1].min():.1f}, {points[:, 1].max():.1f}] Z[{points[:, 2].min():.1f}, {points[:, 2].max():.1f}]")

class GeometricISectionDetector:
    """Geometric-based I-section pile detection using point cloud analysis."""
    
    def __init__(self):
        # I-section pile characteristics
        self.min_height = 1.5  # Minimum pile height above ground
        self.max_height = 10.0  # Maximum reasonable pile height
        self.typical_width = 0.2  # Typical I-section width (~20cm)
        self.width_tolerance = 0.1  # ±10cm tolerance
        self.min_points_per_pile = 50  # Minimum points to consider as pile
        
    def detect_vertical_structures(self, points):
        """Detect vertical structures that could be piles."""
        print("🔍 Detecting vertical structures...")
        
        # Filter by height (structures extending above ground)
        z_min = points[:, 2].min()
        height_mask = (points[:, 2] - z_min) >= self.min_height
        elevated_points = points[height_mask]
        
        print(f"Points above {self.min_height}m: {len(elevated_points):,}")
        
        if len(elevated_points) < self.min_points_per_pile:
            return []
        
        # Cluster elevated points to find individual structures
        clustering = DBSCAN(eps=0.5, min_samples=self.min_points_per_pile)
        cluster_labels = clustering.fit_predict(elevated_points[:, :2])  # Cluster in XY plane
        
        # Extract clusters
        unique_labels = set(cluster_labels)
        unique_labels.discard(-1)  # Remove noise label
        
        vertical_structures = []
        for label in unique_labels:
            cluster_mask = cluster_labels == label
            cluster_points = elevated_points[cluster_mask]
            
            if len(cluster_points) >= self.min_points_per_pile:
                vertical_structures.append(cluster_points)
        
        print(f"Found {len(vertical_structures)} vertical structure candidates")
        return vertical_structures
    
    def analyze_i_section_characteristics(self, structure_points):
        """Analyze if structure has I-section characteristics."""
        # Calculate structure dimensions
        x_span = structure_points[:, 0].max() - structure_points[:, 0].min()
        y_span = structure_points[:, 1].max() - structure_points[:, 1].min()
        z_span = structure_points[:, 2].max() - structure_points[:, 2].min()
        
        # I-section characteristics:
        # 1. Significant height (vertical structure)
        # 2. Moderate width (flange width)
        # 3. Elongated in one horizontal direction (beam orientation)
        
        height_score = min(z_span / self.min_height, 1.0)  # Normalize to [0,1]
        
        # Check if width is reasonable for I-section
        max_horizontal = max(x_span, y_span)
        min_horizontal = min(x_span, y_span)
        
        width_score = 0.0
        if (self.typical_width - self.width_tolerance) <= max_horizontal <= (self.typical_width + self.width_tolerance):
            width_score = 0.8
        
        # I-sections are often elongated (beam length > flange width)
        aspect_ratio = max_horizontal / (min_horizontal + 1e-6)
        elongation_score = min(aspect_ratio / 3.0, 1.0)  # Prefer aspect ratio > 3:1
        
        # Combine scores
        confidence = (height_score * 0.4 + width_score * 0.4 + elongation_score * 0.2)
        
        return {
            'confidence': confidence,
            'height': z_span,
            'width': max_horizontal,
            'thickness': min_horizontal,
            'aspect_ratio': aspect_ratio,
            'point_count': len(structure_points)
        }
    
    def extract_pile_center(self, structure_points):
        """Extract pile center coordinates."""
        # Use centroid of bottom 20% of points (pile base)
        z_min = structure_points[:, 2].min()
        z_threshold = z_min + 0.2 * (structure_points[:, 2].max() - z_min)
        
        base_mask = structure_points[:, 2] <= z_threshold
        base_points = structure_points[base_mask]
        
        if len(base_points) > 0:
            center = base_points.mean(axis=0)
        else:
            center = structure_points.mean(axis=0)
        
        return center
    
    def detect_i_section_piles(self, points):
        """Main detection pipeline."""
        print("🔧 Starting I-section pile detection...")
        
        # Step 1: Find vertical structures
        vertical_structures = self.detect_vertical_structures(points)
        
        if not vertical_structures:
            print("No vertical structures found")
            return []
        
        # Step 2: Analyze each structure for I-section characteristics
        detections = []
        for i, structure in enumerate(vertical_structures):
            analysis = self.analyze_i_section_characteristics(structure)
            
            if analysis['confidence'] >= confidence_threshold:
                center = self.extract_pile_center(structure)
                
                detection = {
                    'x': float(center[0]),
                    'y': float(center[1]),
                    'z': float(center[2]),
                    'confidence': float(analysis['confidence']),
                    'width': float(analysis['width']),
                    'height': float(analysis['height']),
                    'thickness': float(analysis['thickness']),
                    'i_section_score': float(analysis['confidence']),
                    'point_count': int(analysis['point_count']),
                    'detection_method': 'geometric'
                }
                
                detections.append(detection)
        
        print(f"✅ Detected {len(detections)} I-section pile candidates")
        return detections

# Initialize detector
detector = GeometricISectionDetector()
print("✅ Geometric I-section detector initialized")

# Run pile detection
final_detections = detector.detect_i_section_piles(points)

print(f"\n📊 I-SECTION PILE DETECTION RESULTS")
print(f"Total detections: {len(final_detections)}")
print(f"Confidence threshold: {confidence_threshold}")

if final_detections:
    confidences = [d['confidence'] for d in final_detections]
    heights = [d['height'] for d in final_detections]
    widths = [d['width'] for d in final_detections]
    
    print(f"\nConfidence statistics:")
    print(f"  Mean: {np.mean(confidences):.3f}")
    print(f"  Range: [{np.min(confidences):.3f}, {np.max(confidences):.3f}]")
    
    print(f"\nDimension statistics:")
    print(f"  Height range: [{np.min(heights):.2f}, {np.max(heights):.2f}] m")
    print(f"  Width range: [{np.min(widths):.2f}, {np.max(widths):.2f}] m")
    
    # Display individual detections
    print(f"\nDetailed results:")
    for i, det in enumerate(final_detections):
        print(f"  Pile {i+1}: ({det['x']:.1f}, {det['y']:.1f}, {det['z']:.1f}) "
              f"conf={det['confidence']:.3f} h={det['height']:.2f}m w={det['width']:.2f}m")
else:
    print("No piles detected above confidence threshold")
    print("Consider lowering confidence_threshold or checking point cloud quality")

if enable_visualization and final_detections:
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # Plot 1: Top view with detections
    ax1.scatter(points[:, 0], points[:, 1], c=points[:, 2], s=1, alpha=0.6, cmap='viridis')
    
    # Overlay detections
    for det in final_detections:
        circle = plt.Circle((det['x'], det['y']), det['width']/2, 
                          fill=False, color='red', linewidth=2)
        ax1.add_patch(circle)
        ax1.text(det['x'], det['y'], f"{det['confidence']:.2f}", 
                ha='center', va='center', color='red', fontweight='bold')
    
    ax1.set_xlabel('X (m)')
    ax1.set_ylabel('Y (m)')
    ax1.set_title(f'I-Section Pile Detection - Top View\n{len(final_detections)} detections')
    ax1.axis('equal')
    ax1.grid(True, alpha=0.3)
    
    # Plot 2: Side view
    ax2.scatter(points[:, 0], points[:, 2], c='blue', s=1, alpha=0.6)
    
    # Overlay detections
    for det in final_detections:
        ax2.scatter(det['x'], det['z'], c='red', s=100, marker='x', linewidth=3)
        ax2.text(det['x'], det['z'] + 0.5, f"{det['confidence']:.2f}", 
                ha='center', va='bottom', color='red', fontweight='bold')
    
    ax2.set_xlabel('X (m)')
    ax2.set_ylabel('Z (m)')
    ax2.set_title('I-Section Pile Detection - Side View')
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    if save_results:
        plt.savefig(output_path / f'i_section_detection_{ground_method}.png', dpi=300, bbox_inches='tight')
        print(f"📊 Saved visualization: {output_path / f'i_section_detection_{ground_method}.png'}")
    
    plt.show()
else:
    print("No detections to visualize")

if save_results:
    print("\n💾 SAVING RESULTS")
    
    # Save detections CSV
    if final_detections:
        df = pd.DataFrame(final_detections)
        csv_file = output_path / f'i_section_detections_{ground_method}.csv'
        df.to_csv(csv_file, index=False)
        print(f"✅ Saved detections: {csv_file}")
    
    # Save metrics
    metrics = {
        'timestamp': datetime.now().isoformat(),
        'site_name': site_name,
        'ground_method': ground_method,
        'detection_method': 'geometric',
        'confidence_threshold': confidence_threshold,
        'total_detections': len(final_detections),
        'input_points': int(len(points)),
        'detection_statistics': {
            'mean_confidence': float(np.mean([d['confidence'] for d in final_detections])) if final_detections else 0.0,
            'mean_height': float(np.mean([d['height'] for d in final_detections])) if final_detections else 0.0,
            'mean_width': float(np.mean([d['width'] for d in final_detections])) if final_detections else 0.0
        }
    }
    
    metrics_file = output_path / f'i_section_metrics_{ground_method}.json'
    with open(metrics_file, 'w') as f:
        json.dump(metrics, f, indent=2)
    print(f"✅ Saved metrics: {metrics_file}")
    
    print(f"\n🎯 All results saved to: {output_path}")

# Summary
print("\n" + "="*60)
print("🎯 I-SECTION PILE DETECTION SUMMARY")
print("="*60)
print(f"Site: {site_name}")
print(f"Ground method: {ground_method}")
print(f"Detection method: Geometric-based")
print(f"Input points: {len(points):,}")
print(f"Detections found: {len(final_detections)}")
print(f"Success rate: {'✅ GOOD' if len(final_detections) > 0 else '⚠️ NO DETECTIONS'}")
print("="*60)
print("\n🔄 Ready for integration with alignment pipeline!")