{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Batch Alignment Execution Wrapper\n", "\n", "This notebook uses Papermill to execute alignment notebooks across multiple sites, projects, and methods for comprehensive comparison and analysis. It supports batch processing with MLflow tracking for experiment management.\n", "\n", "**Purpose**: Batch execution of alignment methods across different sites  \n", "**Methods**: ICP, Neural Network, Hybrid (Neural + ICP)  \n", "**Output**: Comprehensive results across all combinations  \n", "**Tracking**: MLflow experiment tracking for all runs  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: December 2024  \n", "**Project**: Energy Inspection 3D\n", "\n", "## Batch Processing Features:\n", "1. **Multi-Site Support**: Execute across ENEL and USA project sites\n", "2. **Method Comparison**: Run ICP, Neural Network, and Hybrid methods\n", "3. **MLflow Integration**: Centralized experiment tracking and comparison\n", "4. **Papermill Execution**: Parameterized notebook execution\n", "5. **Results Aggregation**: Collect and compare results across all runs\n", "6. **Error <PERSON>**: Robust execution with failure recovery\n", "7. **Progress Tracking**: Real-time execution status and timing"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Environment Setup\n", "\n", "Import required libraries and configure batch execution environment."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install required packages\n", "!pip install papermill mlflow pandas numpy matplotlib seaborn plotly"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import libraries\n", "import os\n", "import sys\n", "import json\n", "import time\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from pathlib import Path\n", "from datetime import datetime\n", "from typing import List, Dict, Any, Optional\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Papermill and MLflow\n", "try:\n", "    import papermill as pm\n", "    PAPERMILL_AVAILABLE = True\n", "    print(\"Papermill available for batch execution\")\n", "except ImportError:\n", "    PAPERMILL_AVAILABLE = False\n", "    print(\"Papermill not available - install with: pip install papermill\")\n", "\n", "try:\n", "    import mlflow\n", "    MLFLOW_AVAILABLE = True\n", "    print(\"MLflow available for experiment tracking\")\n", "except ImportError:\n", "    MLFLOW_AVAILABLE = False\n", "    print(\"MLflow not available - install with: pip install mlflow\")\n", "\n", "print(f\"Batch Alignment Execution Environment Initialized\")\n", "print(f\"Execution Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. <PERSON><PERSON> Configuration\n", "\n", "Define the configuration for batch execution across sites and methods."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class BatchAlignmentConfig:\n", "    \"\"\"Configuration for batch alignment execution.\"\"\"\n", "    \n", "    def __init__(self):\n", "        # Project configurations\n", "        self.projects = {\n", "            'ENEL': ['<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>'],\n", "            'USA': ['<PERSON>', 'RPCS', 'RES']\n", "        }\n", "        \n", "        # Alignment methods to execute\n", "        self.methods = {\n", "            'icp': {\n", "                'notebook': 'icp_alignment_research.ipynb',\n", "                'experiment_name': 'alignment_icp_batch'\n", "            },\n", "            'neural_network': {\n", "                'notebook': 'neural_network_alignment_research.ipynb',\n", "                'experiment_name': 'alignment_neural_batch'\n", "            },\n", "            'hybrid': {\n", "                'notebook': 'hybrid_neural_icp_alignment_research.ipynb',\n", "                'experiment_name': 'alignment_hybrid_batch'\n", "            }\n", "        }\n", "        \n", "        # Default parameters\n", "        self.default_params = {\n", "            'source_file': 'source_pointcloud.las',\n", "            'target_file': 'target_pointcloud.las',\n", "            'enable_visualization': False,  # Disable for batch processing\n", "            'save_intermediate': True\n", "        }\n", "        \n", "        # Method-specific parameters\n", "        self.method_params = {\n", "            'icp': {\n", "                'max_iterations': 50,\n", "                'tolerance': 1e-6,\n", "                'voxel_size': 0.02,\n", "                'distance_threshold': 0.1\n", "            },\n", "            'neural_network': {\n", "                'num_points': 1024,\n", "                'batch_size': 32,\n", "                'epochs': 50,  # Reduced for batch processing\n", "                'learning_rate': 0.001,\n", "                'enable_training': False  # Use pre-trained models\n", "            },\n", "            'hybrid': {\n", "                'num_points': 1024,\n", "                'icp_max_iterations': 20,\n", "                'icp_tolerance': 1e-6,\n", "                'icp_voxel_size': 0.02,\n", "                'use_downsampling': True\n", "            }\n", "        }\n", "        \n", "        # Paths\n", "        self.base_path = Path('../..')\n", "        self.notebooks_dir = Path('.')\n", "        self.output_dir = self.base_path / 'output_runs' / 'alignment_batch'\n", "        self.batch_results_dir = self.output_dir / 'batch_results'\n", "        \n", "        # Create directories\n", "        self.output_dir.mkdir(parents=True, exist_ok=True)\n", "        self.batch_results_dir.mkdir(parents=True, exist_ok=True)\n", "        \n", "        # Execution settings\n", "        self.parallel_execution = False  # Set to True for parallel processing\n", "        self.max_workers = 4\n", "        self.timeout_minutes = 30  # Timeout per notebook execution\n", "        \n", "        print(f\"Batch configuration initialized\")\n", "        print(f\"Projects: {sum(len(sites) for sites in self.projects.values())} sites across {len(self.projects)} project types\")\n", "        print(f\"Methods: {list(self.methods.keys())}\")\n", "        print(f\"Output directory: {self.output_dir}\")\n", "\n", "config = BatchAlignmentConfig()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Batch Execution Functions\n", "\n", "Implement functions for executing notebooks across different configurations."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def generate_execution_parameters(project_type: str, project_name: str, method: str, config: BatchAlignmentConfig) -> Dict[str, Any]:\n", "    \"\"\"\n", "    Generate parameters for notebook execution.\n", "    \n", "    Parameters:\n", "    -----------\n", "    project_type : str\n", "        Project type (ENEL, USA)\n", "    project_name : str\n", "        Project name\n", "    method : str\n", "        Alignment method\n", "    config : BatchAlignmentConfig\n", "        Batch configuration\n", "    \n", "    Returns:\n", "    --------\n", "    parameters : dict\n", "        Parameters for Papermill execution\n", "    \"\"\"\n", "    # Base parameters\n", "    parameters = {\n", "        'project_type': project_type,\n", "        'project_name': project_name,\n", "        'output_dir': str(config.output_dir),\n", "        'mlflow_experiment_name': config.methods[method]['experiment_name'],\n", "        'mlflow_run_name': f\"{method}_{project_type}_{project_name}\"\n", "    }\n", "    \n", "    # Add default parameters\n", "    parameters.update(config.default_params)\n", "    \n", "    # Add method-specific parameters\n", "    if method in config.method_params:\n", "        parameters.update(config.method_params[method])\n", "    \n", "    return parameters"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def execute_single_notebook(project_type: str, project_name: str, method: str, config: BatchAlignmentConfig) -> Dict[str, Any]:\n", "    \"\"\"\n", "    Execute a single notebook with given parameters.\n", "    \n", "    Parameters:\n", "    -----------\n", "    project_type : str\n", "        Project type\n", "    project_name : str\n", "        Project name\n", "    method : str\n", "        Alignment method\n", "    config : BatchAlignmentConfig\n", "        Batch configuration\n", "    \n", "    Returns:\n", "    --------\n", "    result : dict\n", "        Execution result with status and metadata\n", "    \"\"\"\n", "    if not PAPERMILL_AVAILABLE:\n", "        return {\n", "            'status': 'error',\n", "            'error': 'Papermill not available',\n", "            'execution_time': 0\n", "        }\n", "    \n", "    # Generate parameters\n", "    parameters = generate_execution_parameters(project_type, project_name, method, config)\n", "    \n", "    # Setup paths\n", "    input_notebook = config.notebooks_dir / config.methods[method]['notebook']\n", "    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "    output_notebook = config.batch_results_dir / f\"{method}_{project_type}_{project_name}_{timestamp}.ipynb\"\n", "    \n", "    print(f\"\\nExecuting: {method} for {project_type}/{project_name}\")\n", "    print(f\"Input: {input_notebook}\")\n", "    print(f\"Output: {output_notebook}\")\n", "    \n", "    start_time = time.time()\n", "    \n", "    try:\n", "        # Execute notebook with Papermill\n", "        pm.execute_notebook(\n", "            input_path=str(input_notebook),\n", "            output_path=str(output_notebook),\n", "            parameters=parameters,\n", "            kernel_name='python3',\n", "            request_save_on_cell_execute=True,\n", "            progress_bar=False\n", "        )\n", "        \n", "        execution_time = time.time() - start_time\n", "        \n", "        result = {\n", "            'status': 'success',\n", "            'project_type': project_type,\n", "            'project_name': project_name,\n", "            'method': method,\n", "            'input_notebook': str(input_notebook),\n", "            'output_notebook': str(output_notebook),\n", "            'parameters': parameters,\n", "            'execution_time': execution_time,\n", "            'timestamp': datetime.now().isoformat(),\n", "            'error': None\n", "        }\n", "        \n", "        print(f\"✓ Completed in {execution_time:.1f}s\")\n", "        return result\n", "        \n", "    except Exception as e:\n", "        execution_time = time.time() - start_time\n", "        \n", "        result = {\n", "            'status': 'error',\n", "            'project_type': project_type,\n", "            'project_name': project_name,\n", "            'method': method,\n", "            'input_notebook': str(input_notebook),\n", "            'output_notebook': str(output_notebook),\n", "            'parameters': parameters,\n", "            'execution_time': execution_time,\n", "            'timestamp': datetime.now().isoformat(),\n", "            'error': str(e)\n", "        }\n", "        \n", "        print(f\"✗ Failed after {execution_time:.1f}s: {e}\")\n", "        return result"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Execute Batch Processing\n", "\n", "Run the complete batch execution workflow."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Configuration for this batch run\n", "# You can modify these to run specific subsets\n", "\n", "# Option 1: Run all projects and methods (full batch)\n", "selected_projects = None  # Use all projects\n", "selected_methods = None   # Use all methods\n", "\n", "# Option 2: Run specific projects only\n", "# selected_projects = {'ENEL': ['<PERSON>no', 'Castro']}  # Subset of projects\n", "# selected_methods = ['icp', 'hybrid']  # Subset of methods\n", "\n", "# Option 3: Test run with minimal configuration\n", "# selected_projects = {'ENEL': ['Trino']}\n", "# selected_methods = ['icp']\n", "\n", "print(\"Starting batch alignment execution...\")\n", "print(f\"Selected projects: {selected_projects if selected_projects else 'All'}\")\n", "print(f\"Selected methods: {selected_methods if selected_methods else 'All'}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def execute_batch_alignment(config: BatchAlignmentConfig, \n", "                          selected_projects: Optional[Dict[str, List[str]]] = None,\n", "                          selected_methods: Optional[List[str]] = None) -> List[Dict[str, Any]]:\n", "    \"\"\"\n", "    Execute batch alignment across multiple projects and methods.\n", "    \n", "    Parameters:\n", "    -----------\n", "    config : BatchAlignmentConfig\n", "        Batch configuration\n", "    selected_projects : dict, optional\n", "        Subset of projects to execute (if None, executes all)\n", "    selected_methods : list, optional\n", "        Subset of methods to execute (if None, executes all)\n", "    \n", "    Returns:\n", "    --------\n", "    results : list\n", "        List of execution results\n", "    \"\"\"\n", "    # Use all projects and methods if not specified\n", "    projects = selected_projects if selected_projects is not None else config.projects\n", "    methods = selected_methods if selected_methods is not None else list(config.methods.keys())\n", "    \n", "    # Calculate total executions\n", "    total_executions = sum(len(sites) for sites in projects.values()) * len(methods)\n", "    \n", "    print(f\"\\n=== Starting Batch Alignment Execution ===\")\n", "    print(f\"Projects: {projects}\")\n", "    print(f\"Methods: {methods}\")\n", "    print(f\"Total executions: {total_executions}\")\n", "    print(f\"Estimated time: {total_executions * 5:.0f}-{total_executions * 15:.0f} minutes\")\n", "    \n", "    results = []\n", "    execution_count = 0\n", "    start_time = time.time()\n", "    \n", "    # Execute all combinations\n", "    for project_type, project_names in projects.items():\n", "        for project_name in project_names:\n", "            for method in methods:\n", "                execution_count += 1\n", "                \n", "                print(f\"\\n[{execution_count}/{total_executions}] {project_type}/{project_name} - {method}\")\n", "                \n", "                # Execute single notebook\n", "                result = execute_single_notebook(project_type, project_name, method, config)\n", "                results.append(result)\n", "                \n", "                # Progress update\n", "                elapsed_time = time.time() - start_time\n", "                avg_time_per_execution = elapsed_time / execution_count\n", "                remaining_executions = total_executions - execution_count\n", "                estimated_remaining_time = remaining_executions * avg_time_per_execution\n", "                \n", "                print(f\"Progress: {execution_count}/{total_executions} ({execution_count/total_executions*100:.1f}%)\")\n", "                print(f\"Elapsed: {elapsed_time/60:.1f}m, Estimated remaining: {estimated_remaining_time/60:.1f}m\")\n", "    \n", "    total_time = time.time() - start_time\n", "    \n", "    print(f\"\\n=== Batch Execution Completed ===\")\n", "    print(f\"Total time: {total_time/60:.1f} minutes\")\n", "    print(f\"Average time per execution: {total_time/total_executions:.1f} seconds\")\n", "    \n", "    # Summary statistics\n", "    successful = sum(1 for r in results if r['status'] == 'success')\n", "    failed = sum(1 for r in results if r['status'] == 'error')\n", "    \n", "    print(f\"\\nExecution Summary:\")\n", "    print(f\"  Successful: {successful}/{total_executions} ({successful/total_executions*100:.1f}%)\")\n", "    print(f\"  Failed: {failed}/{total_executions} ({failed/total_executions*100:.1f}%)\")\n", "    \n", "    return results"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Execute batch processing\n", "if PAPERMILL_AVAILABLE:\n", "    # Run batch execution\n", "    batch_results = execute_batch_alignment(\n", "        config=config,\n", "        selected_projects=selected_projects,\n", "        selected_methods=selected_methods\n", "    )\n", "    \n", "    # Save results to files\n", "    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')\n", "    results_file = config.batch_results_dir / f'batch_results_{timestamp}.json'\n", "    \n", "    with open(results_file, 'w') as f:\n", "        json.dump(batch_results, f, indent=2, default=str)\n", "    \n", "    print(f\"\\n=== Batch Execution Complete ===\")\n", "    print(f\"Results saved to: {results_file}\")\n", "    print(f\"Individual notebooks saved to: {config.batch_results_dir}\")\n", "    \n", "    # Display summary\n", "    successful = sum(1 for r in batch_results if r['status'] == 'success')\n", "    failed = sum(1 for r in batch_results if r['status'] == 'error')\n", "    total = len(batch_results)\n", "    \n", "    print(f\"\\nFinal Summary:\")\n", "    print(f\"  Total executions: {total}\")\n", "    print(f\"  Successful: {successful} ({successful/total*100:.1f}%)\")\n", "    print(f\"  Failed: {failed} ({failed/total*100:.1f}%)\")\n", "    \n", "    if failed > 0:\n", "        print(f\"\\nFailed executions:\")\n", "        for result in batch_results:\n", "            if result['status'] == 'error':\n", "                print(f\"  {result['project_type']}/{result['project_name']} - {result['method']}: {result['error']}\")\n", "    \n", "else:\n", "    print(\"Papermill not available. Please install with: pip install papermill\")\n", "    print(\"\\nTo run manually, use the following example parameters:\")\n", "    \n", "    # Show example parameters for manual execution\n", "    example_params = generate_execution_parameters('ENEL', 'Trino', 'icp', config)\n", "    print(f\"\\nExample parameters for ENEL/Trino - ICP:\")\n", "    for key, value in example_params.items():\n", "        print(f\"  {key}: {value}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}