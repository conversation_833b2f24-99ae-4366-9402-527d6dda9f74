{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Ground Method Alignment Results Compilation and Analysis\n", "\n", "This notebook compiles and analyzes results from systematic testing of ground segmentation methods against alignment algorithms. Use this after completing the manual execution guide.\n", "\n", "**Input**: MLflow logs and saved notebook results from systematic testing  \n", "**Output**: Comprehensive analysis and recommendations  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup and Dependencies"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from pathlib import Path\n", "import mlflow\n", "import mlflow.tracking\n", "import logging\n", "from datetime import datetime\n", "import json\n", "\n", "# Setup logging\n", "logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)\n", "\n", "# Set style for plots\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Manual Results Input\n", "\n", "**Instructions**: Fill in the results from your systematic testing following the execution guide."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Manual results input - fill this based on your testing\n", "manual_results = [\n", "    # CSF Results\n", "    {'ground_method': 'csf', 'alignment_method': 'icp', 'success': True, 'rmse': 0.0, 'time': 0.0, 'iterations': 0, 'point_count': 0, 'notes': ''},\n", "    {'ground_method': 'csf', 'alignment_method': 'neural_network', 'success': True, 'rmse': 0.0, 'time': 0.0, 'iterations': None, 'point_count': 0, 'notes': ''},\n", "    {'ground_method': 'csf', 'alignment_method': 'hybrid', 'success': True, 'rmse': 0.0, 'time': 0.0, 'iterations': 0, 'point_count': 0, 'notes': ''},\n", "    \n", "    # PMF Results\n", "    {'ground_method': 'pmf', 'alignment_method': 'icp', 'success': False, 'rmse': 0.0, 'time': 0.0, 'iterations': 0, 'point_count': 0, 'notes': ''},\n", "    {'ground_method': 'pmf', 'alignment_method': 'neural_network', 'success': False, 'rmse': 0.0, 'time': 0.0, 'iterations': None, 'point_count': 0, 'notes': ''},\n", "    {'ground_method': 'pmf', 'alignment_method': 'hybrid', 'success': False, 'rmse': 0.0, 'time': 0.0, 'iterations': 0, 'point_count': 0, 'notes': ''},\n", "    \n", "    # RANSAC Results\n", "    {'ground_method': 'ransac', 'alignment_method': 'icp', 'success': True, 'rmse': 0.0, 'time': 0.0, 'iterations': 0, 'point_count': 0, 'notes': ''},\n", "    {'ground_method': 'ransac', 'alignment_method': 'neural_network', 'success': True, 'rmse': 0.0, 'time': 0.0, 'iterations': None, 'point_count': 0, 'notes': ''},\n", "    {'ground_method': 'ransac', 'alignment_method': 'hybrid', 'success': True, 'rmse': 0.0, 'time': 0.0, 'iterations': 0, 'point_count': 0, 'notes': ''},\n", "    \n", "    # RANSAC+PMF Results\n", "    {'ground_method': 'ransac_pmf', 'alignment_method': 'icp', 'success': True, 'rmse': 0.0, 'time': 0.0, 'iterations': 0, 'point_count': 0, 'notes': ''},\n", "    {'ground_method': 'ransac_pmf', 'alignment_method': 'neural_network', 'success': True, 'rmse': 0.0, 'time': 0.0, 'iterations': None, 'point_count': 0, 'notes': ''},\n", "    {'ground_method': 'ransac_pmf', 'alignment_method': 'hybrid', 'success': True, 'rmse': 0.0, 'time': 0.0, 'iterations': 0, 'point_count': 0, 'notes': ''}\n", "]\n", "\n", "# Convert to DataFrame\n", "results_df = pd.DataFrame(manual_results)\n", "\n", "logger.info(f\"Loaded {len(results_df)} manual test results\")\n", "print(\"\\nCurrent Results (UPDATE WITH YOUR ACTUAL VALUES):\")\n", "print(results_df[['ground_method', 'alignment_method', 'success', 'rmse', 'time']].round(3))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## MLflow Results Retrieval (Optional)\n", "\n", "If you used MLflow during testing, this section can automatically retrieve results."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def retrieve_mlflow_results():\n", "    \"\"\"Retrieve results from MLflow experiments\"\"\"\n", "    \n", "    try:\n", "        # Set MLflow tracking URI if needed\n", "        # mlflow.set_tracking_uri(\"your_mlflow_uri\")\n", "        \n", "        experiments = [\n", "            \"alignment_icp\",\n", "            \"alignment_neural_network\", \n", "            \"alignment_hybrid\",\n", "            \"alignment_comparison\"\n", "        ]\n", "        \n", "        mlflow_results = []\n", "        \n", "        for exp_name in experiments:\n", "            try:\n", "                experiment = mlflow.get_experiment_by_name(exp_name)\n", "                if experiment:\n", "                    runs = mlflow.search_runs(experiment_ids=[experiment.experiment_id])\n", "                    \n", "                    for _, run in runs.iterrows():\n", "                        # Extract ground method from run name\n", "                        run_name = run.get('tags.mlflow.runName', '')\n", "                        \n", "                        ground_method = None\n", "                        for method in ['csf', 'pmf', 'ransac_pmf', 'ransac']:\n", "                            if method in run_name:\n", "                                ground_method = method\n", "                                break\n", "                        \n", "                        if ground_method:\n", "                            result = {\n", "                                'ground_method': ground_method,\n", "                                'alignment_method': exp_name.replace('alignment_', ''),\n", "                                'success': run.get('metrics.convergence_success', 0) == 1,\n", "                                'rmse': run.get('metrics.final_rmse', np.nan),\n", "                                'time': run.get('metrics.total_time', np.nan),\n", "                                'iterations': run.get('metrics.iterations', np.nan),\n", "                                'run_id': run['run_id']\n", "                            }\n", "                            mlflow_results.append(result)\n", "                            \n", "            except Exception as e:\n", "                logger.warning(f\"Could not retrieve experiment {exp_name}: {e}\")\n", "        \n", "        if mlflow_results:\n", "            logger.info(f\"Retrieved {len(mlflow_results)} results from MLflow\")\n", "            return pd.DataFrame(mlflow_results)\n", "        else:\n", "            logger.info(\"No MLflow results found\")\n", "            return pd.DataFrame()\n", "            \n", "    except Exception as e:\n", "        logger.warning(f\"MLflow retrieval failed: {e}\")\n", "        return pd.DataFrame()\n", "\n", "# Try to retrieve MLflow results\n", "mlflow_df = retrieve_mlflow_results()\n", "\n", "if not mlflow_df.empty:\n", "    logger.info(\"MLflow results available - you can use these instead of manual input\")\n", "    print(\"\\nMLflow Results:\")\n", "    print(mlflow_df[['ground_method', 'alignment_method', 'success', 'rmse', 'time']].round(3))\n", "else:\n", "    logger.info(\"Using manual results only\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Results Analysis and Visualization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Use MLflow results if available, otherwise use manual results\n", "analysis_df = mlflow_df if not mlflow_df.empty else results_df\n", "\n", "if analysis_df.empty or analysis_df['rmse'].sum() == 0:\n", "    logger.warning(\"No valid results for analysis. Please update the manual_results with your actual test data.\")\n", "else:\n", "    logger.info(\"Proceeding with results analysis...\")\n", "    \n", "    # Calculate success rates\n", "    success_summary = analysis_df.groupby(['ground_method', 'alignment_method'])['success'].agg(['mean', 'count']).reset_index()\n", "    success_summary.columns = ['ground_method', 'alignment_method', 'success_rate', 'test_count']\n", "    \n", "    # Calculate performance metrics for successful runs only\n", "    successful_runs = analysis_df[analysis_df['success'] == True]\n", "    \n", "    if not successful_runs.empty:\n", "        performance_summary = successful_runs.groupby(['ground_method', 'alignment_method']).agg({\n", "            'rmse': ['mean', 'std'],\n", "            'time': ['mean', 'std'],\n", "            'iterations': 'mean'\n", "        }).round(3)\n", "        \n", "        # Flatten column names\n", "        performance_summary.columns = ['_'.join(col).strip() for col in performance_summary.columns]\n", "        performance_summary = performance_summary.reset_index()\n", "        \n", "        # Merge with success rates\n", "        final_summary = success_summary.merge(performance_summary, on=['ground_method', 'alignment_method'], how='left')\n", "        \n", "        print(\"\\n=== COMPREHENSIVE RESULTS SUMMARY ===\")\n", "        display_cols = ['ground_method', 'alignment_method', 'success_rate', 'rmse_mean', 'time_mean']\n", "        print(final_summary[display_cols].round(3))\n", "    else:\n", "        logger.warning(\"No successful runs found for performance analysis\")\n", "        final_summary = success_summary"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create visualization if we have valid data\n", "if not analysis_df.empty and analysis_df['rmse'].sum() > 0:\n", "    \n", "    fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "    fig.suptitle('Ground Segmentation Method Impact on Alignment Performance', fontsize=16, fontweight='bold')\n", "    \n", "    # 1. Success Rate Heatmap\n", "    ax1 = axes[0, 0]\n", "    success_pivot = analysis_df.groupby(['ground_method', 'alignment_method'])['success'].mean().unstack()\n", "    sns.heatmap(success_pivot, annot=True, fmt='.2f', cmap='RdYlGn', ax=ax1, cbar_kws={'label': 'Success Rate'})\n", "    ax1.set_title('Success Rate by Method Combination')\n", "    ax1.set_xlabel('Alignment Method')\n", "    ax1.set_ylabel('Ground Segmentation Method')\n", "    \n", "    # 2. <PERSON><PERSON><PERSON> (successful runs only)\n", "    ax2 = axes[0, 1]\n", "    if not successful_runs.empty:\n", "        rmse_pivot = successful_runs.groupby(['ground_method', 'alignment_method'])['rmse'].mean().unstack()\n", "        sns.heatmap(rmse_pivot, annot=True, fmt='.3f', cmap='RdYlGn_r', ax=ax2, cbar_kws={'label': 'RMSE (m)'})\n", "    ax2.set_title('Average RMSE (Successful Runs Only)')\n", "    ax2.set_xlabel('Alignment Method')\n", "    ax2.set_ylabel('Ground Segmentation Method')\n", "    \n", "    # 3. Performance by Ground Method\n", "    ax3 = axes[1, 0]\n", "    ground_performance = analysis_df.groupby('ground_method')['success'].mean()\n", "    bars = ax3.bar(ground_performance.index, ground_performance.values, \n", "                   color=['#2E8B57', '#4169E1', '#9932CC', '#FF8C00'])\n", "    ax3.set_title('Overall Success Rate by Ground Method')\n", "    ax3.set_ylabel('Success Rate')\n", "    ax3.set_ylim(0, 1)\n", "    \n", "    # Add value labels on bars\n", "    for bar, value in zip(bars, ground_performance.values):\n", "        ax3.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 0.02,\n", "                f'{value:.2f}', ha='center', va='bottom', fontweight='bold')\n", "    \n", "    # 4. Performance by Alignment Method\n", "    ax4 = axes[1, 1]\n", "    alignment_performance = analysis_df.groupby('alignment_method')['success'].mean()\n", "    bars = ax4.bar(alignment_performance.index, alignment_performance.values,\n", "                   color=['#DC143C', '#32CD32', '#FF69B4'])\n", "    ax4.set_title('Overall Success Rate by Alignment Method')\n", "    ax4.set_ylabel('Success Rate')\n", "    ax4.set_ylim(0, 1)\n", "    \n", "    # Add value labels on bars\n", "    for bar, value in zip(bars, alignment_performance.values):\n", "        ax4.text(bar.get_x() + bar.get_width()/2., bar.get_height() + 0.02,\n", "                f'{value:.2f}', ha='center', va='bottom', fontweight='bold')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Save plot\n", "    output_path = Path('../../data/processed/trino_enel/analysis')\n", "    output_path.mkdir(parents=True, exist_ok=True)\n", "    plot_path = output_path / \"ground_method_alignment_results.png\"\n", "    fig.savefig(plot_path, dpi=300, bbox_inches='tight')\n", "    logger.info(f\"Saved results plot: {plot_path}\")\n", "    \n", "else:\n", "    logger.warning(\"Skipping visualization - no valid data available\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}