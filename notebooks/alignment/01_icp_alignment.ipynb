{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# ICP Point Cloud Alignment - Exploration\n", "\n", "Simple exploration of ICP alignment between ground-segmented point clouds and IFC models.\n", "\n", "**Goal**: Align non-ground points (structures) with IFC point cloud  \n", "**Method**: ICP with coordinate system pre-alignment  \n", "**Output**: Aligned point cloud and basic metrics  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Parameters (can be set by Papermill)\n", "ground_method = \"csf\"  # Ground segmentation method: csf, pmf, ransac\n", "site_name = \"trino_enel\"\n", "max_iterations = 50\n", "tolerance = 1e-6\n", "voxel_size = 0.02  # 0 = no downsampling\n", "output_dir = \"../../data/output_runs/alignment_testing\""]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ICP Alignment Exploration - CSF\n", "Output: ../../data/output_runs/alignment_testing/csf\n"]}], "source": ["# Imports\n", "import numpy as np\n", "import open3d as o3d\n", "import matplotlib.pyplot as plt\n", "import time\n", "import json\n", "from pathlib import Path\n", "from scipy.spatial import cKDTree\n", "from datetime import datetime\n", "\n", "# Setup\n", "np.random.seed(42)\n", "output_path = Path(output_dir) / ground_method\n", "output_path.mkdir(parents=True, exist_ok=True)\n", "\n", "print(f\"ICP Alignment Exploration - {ground_method.upper()}\")\n", "print(f\"Output: {output_path}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load Point Clouds"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Source: ../../data/processed/trino_enel/ground_segmentation/csf/trino_enel_nonground.ply\n", "Target: ../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\n", "Source exists: True\n", "Target exists: True\n"]}], "source": ["# Find source file (non-ground points)\n", "source_patterns = [\n", "    f\"../../data/processed/{site_name}/ground_segmentation/{ground_method}/trino_enel_nonground.ply\",\n", "    f\"../../data/processed/{site_name}/ground_segmentation/{ground_method}/*nonground*.ply\"\n", "]\n", "\n", "source_file = None\n", "for pattern in source_patterns:\n", "    files = list(Path().glob(pattern))\n", "    if files:\n", "        source_file = files[0]\n", "        break\n", "\n", "# Target file (IFC point cloud)\n", "target_file = Path(f\"../../data/processed/{site_name}/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\")\n", "\n", "print(f\"Source: {source_file}\")\n", "print(f\"Target: {target_file}\")\n", "print(f\"Source exists: {source_file.exists() if source_file else False}\")\n", "print(f\"Target exists: {target_file.exists()}\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded source: 13,848 points\n", "Loaded target: 118,920 points\n", "\n", "Source bounds: X[435223.7, 436794.1] Y[5010816.9, 5012539.1] Z[1.2, 13.1]\n", "Target bounds: X[435267.2, 436720.0] Y[5010900.7, 5012462.4] Z[152.8, 161.7]\n"]}], "source": ["# Load point clouds\n", "if source_file and source_file.exists() and target_file.exists():\n", "    source_pcd = o3d.io.read_point_cloud(str(source_file))\n", "    target_pcd = o3d.io.read_point_cloud(str(target_file))\n", "    \n", "    source_points = np.asarray(source_pcd.points)\n", "    target_points = np.asarray(target_pcd.points)\n", "    \n", "    print(f\"Loaded source: {source_points.shape[0]:,} points\")\n", "    print(f\"Loaded target: {target_points.shape[0]:,} points\")\n", "    \n", "    # Basic statistics\n", "    print(f\"\\nSource bounds: X[{source_points[:,0].min():.1f}, {source_points[:,0].max():.1f}] Y[{source_points[:,1].min():.1f}, {source_points[:,1].max():.1f}] Z[{source_points[:,2].min():.1f}, {source_points[:,2].max():.1f}]\")\n", "    print(f\"Target bounds: X[{target_points[:,0].min():.1f}, {target_points[:,0].max():.1f}] Y[{target_points[:,1].min():.1f}, {target_points[:,1].max():.1f}] Z[{target_points[:,2].min():.1f}, {target_points[:,2].max():.1f}]\")\n", "else:\n", "    print(\"ERROR: Could not load point clouds!\")\n", "    print(\"Creating dummy data for testing...\")\n", "    \n", "    # Create dummy data\n", "    source_points = np.random.randn(1000, 3) * 100\n", "    target_points = np.random.randn(800, 3) * 100 + [50, 50, 0]\n", "    \n", "    print(f\"Using dummy data: source={source_points.shape[0]}, target={target_points.shape[0]}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Preprocessing"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Downsampling with voxel size: 0.02\n", "After downsampling: source=13,848, target=118,916\n"]}], "source": ["# Downsample if needed\n", "if voxel_size > 0:\n", "    print(f\"Downsampling with voxel size: {voxel_size}\")\n", "    \n", "    source_pcd_down = o3d.geometry.PointCloud()\n", "    source_pcd_down.points = o3d.utility.Vector3dVector(source_points)\n", "    source_pcd_down = source_pcd_down.voxel_down_sample(voxel_size)\n", "    \n", "    target_pcd_down = o3d.geometry.PointCloud()\n", "    target_pcd_down.points = o3d.utility.Vector3dVector(target_points)\n", "    target_pcd_down = target_pcd_down.voxel_down_sample(voxel_size)\n", "    \n", "    source_work = np.asarray(source_pcd_down.points)\n", "    target_work = np.asarray(target_pcd_down.points)\n", "    \n", "    print(f\"After downsampling: source={source_work.shape[0]:,}, target={target_work.shape[0]:,}\")\n", "else:\n", "    source_work = source_points.copy()\n", "    target_work = target_points.copy()\n", "    print(\"No downsampling applied\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Pre-aligning coordinate systems...\n", "Source centroid: [436024.7, 5011683.3, 2.5]\n", "Target centroid: [435986.3, 5011746.7, 157.3]\n", "Scale ratio (target/source): 0.972\n", "No scale normalization needed\n", "Pre-alignment complete. Scale factor: 1.000\n"]}], "source": ["# Pre-align coordinate systems\n", "print(\"\\nPre-aligning coordinate systems...\")\n", "\n", "# Calculate centroids\n", "source_centroid = np.mean(source_work, axis=0)\n", "target_centroid = np.mean(target_work, axis=0)\n", "\n", "print(f\"Source centroid: [{source_centroid[0]:.1f}, {source_centroid[1]:.1f}, {source_centroid[2]:.1f}]\")\n", "print(f\"Target centroid: [{target_centroid[0]:.1f}, {target_centroid[1]:.1f}, {target_centroid[2]:.1f}]\")\n", "\n", "# Center both point clouds\n", "source_centered = source_work - source_centroid\n", "target_centered = target_work - target_centroid\n", "\n", "# Check scale difference\n", "source_scale = np.max(np.linalg.norm(source_centered, axis=1))\n", "target_scale = np.max(np.linalg.norm(target_centered, axis=1))\n", "scale_ratio = target_scale / source_scale if source_scale > 0 else 1.0\n", "\n", "print(f\"Scale ratio (target/source): {scale_ratio:.3f}\")\n", "\n", "# Apply scale if significant difference\n", "if scale_ratio > 2.0 or scale_ratio < 0.5:\n", "    print(f\"Applying scale normalization: {scale_ratio:.3f}\")\n", "    source_centered *= scale_ratio\n", "    scale_applied = scale_ratio\n", "else:\n", "    scale_applied = 1.0\n", "    print(\"No scale normalization needed\")\n", "\n", "print(f\"Pre-alignment complete. Scale factor: {scale_applied:.3f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## ICP Algorithm"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Running ICP alignment...\n", "Iter  1: MSE = 2.329079e+03\n", "Iter  2: MSE = 1.825125e+03\n", "Iter  3: MSE = 1.529049e+03\n", "Iter  4: MSE = 1.371141e+03\n", "Iter  5: MSE = 1.285444e+03\n", "Iter  6: MSE = 1.239009e+03\n", "Iter  7: MSE = 1.212939e+03\n", "Iter  8: MSE = 1.199280e+03\n", "Iter  9: MSE = 1.191323e+03\n", "Iter 10: MSE = 1.186096e+03\n", "Iter 11: MSE = 1.183115e+03\n", "Iter 12: MSE = 1.181465e+03\n", "Iter 13: MSE = 1.180598e+03\n", "Iter 14: MSE = 1.180108e+03\n", "Iter 15: MSE = 1.179768e+03\n", "Iter 16: MSE = 1.179543e+03\n", "Iter 17: MSE = 1.179402e+03\n", "Iter 18: MSE = 1.179340e+03\n", "Iter 19: MSE = 1.179310e+03\n", "Iter 20: MSE = 1.179294e+03\n", "Iter 21: MSE = 1.179285e+03\n", "Iter 22: MSE = 1.179277e+03\n", "Iter 23: MSE = 1.179271e+03\n", "Iter 24: MSE = 1.179262e+03\n", "Iter 25: MSE = 1.179254e+03\n", "Iter 26: MSE = 1.179241e+03\n", "Iter 27: MSE = 1.179217e+03\n", "Iter 28: MSE = 1.179172e+03\n", "Iter 29: MSE = 1.179104e+03\n", "Iter 30: MSE = 1.179033e+03\n", "Iter 31: MSE = 1.178976e+03\n", "Iter 32: MSE = 1.178944e+03\n", "Iter 33: MSE = 1.178927e+03\n", "Iter 34: MSE = 1.178919e+03\n", "Iter 35: MSE = 1.178915e+03\n", "Iter 36: MSE = 1.178913e+03\n", "Iter 37: MSE = 1.178912e+03\n", "Iter 38: MSE = 1.178912e+03\n", "Iter 39: MSE = 1.178911e+03\n", "Iter 40: MSE = 1.178911e+03\n", "Iter 41: MSE = 1.178911e+03\n", "Iter 42: MSE = 1.178911e+03\n", "Iter 43: MSE = 1.178911e+03\n", "Iter 44: MSE = 1.178911e+03\n", "Iter 45: MSE = 1.178911e+03\n", "Iter 46: MSE = 1.178911e+03\n", "Converged after 46 iterations\n", "\n", "ICP completed in 2.14s\n", "Iterations: 46\n", "Final MSE: 1.178911e+03\n"]}], "source": ["# Simple ICP implementation\n", "def simple_icp(source, target, max_iter=50, tolerance=1e-6):\n", "    \"\"\"\n", "    Basic ICP implementation for exploration\n", "    \"\"\"\n", "    source_current = source.copy()\n", "    prev_error = float('inf')\n", "    errors = []\n", "    T_total = np.eye(4)\n", "    \n", "    start_time = time.time()\n", "    \n", "    for i in range(max_iter):\n", "        # Find closest points\n", "        tree = cKDTree(target)\n", "        distances, indices = tree.query(source_current)\n", "        \n", "        # Calculate error\n", "        mse = np.mean(distances**2)\n", "        errors.append(mse)\n", "        \n", "        print(f\"Iter {i+1:2d}: MSE = {mse:.6e}\")\n", "        \n", "        # Check convergence\n", "        if abs(prev_error - mse) < tolerance:\n", "            print(f\"Converged after {i+1} iterations\")\n", "            break\n", "        prev_error = mse\n", "        \n", "        # Get corresponding points\n", "        target_matched = target[indices]\n", "        \n", "        # Calculate centroids\n", "        source_mean = np.mean(source_current, axis=0)\n", "        target_mean = np.mean(target_matched, axis=0)\n", "        \n", "        # Center points\n", "        source_centered = source_current - source_mean\n", "        target_centered = target_matched - target_mean\n", "        \n", "        # Calculate rotation using SVD\n", "        H = np.dot(source_centered.T, target_centered)\n", "        U, S, Vt = np.linalg.svd(H)\n", "        R = np.dot(Vt.T, U.T)\n", "        \n", "        # Ensure proper rotation\n", "        if np.linalg.det(R) < 0:\n", "            Vt[-1, :] *= -1\n", "            R = np.dot(Vt.T, U.T)\n", "        \n", "        # Calculate translation\n", "        t = target_mean - np.dot(R, source_mean)\n", "        \n", "        # Apply transformation\n", "        source_current = np.dot(source_current, R.T) + t\n", "        \n", "        # Update total transformation\n", "        T = np.eye(4)\n", "        T[:3, :3] = R\n", "        T[:3, 3] = t\n", "        T_total = np.dot(T, T_total)\n", "    \n", "    elapsed = time.time() - start_time\n", "    return T_total, source_current, mse, i+1, errors, elapsed\n", "\n", "# Run ICP\n", "print(\"\\nRunning ICP alignment...\")\n", "T_icp, aligned_source, final_mse, iterations, convergence_history, icp_time = simple_icp(\n", "    source_centered, target_centered, max_iterations, tolerance\n", ")\n", "\n", "print(f\"\\nICP completed in {icp_time:.2f}s\")\n", "print(f\"Iterations: {iterations}\")\n", "print(f\"Final MSE: {final_mse:.6e}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}