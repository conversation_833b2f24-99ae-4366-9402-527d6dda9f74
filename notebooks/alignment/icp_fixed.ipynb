{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# ICP Alignment - Fixed Version\n", "\n", "**Fixes Applied:**\n", "- No double downsampling (uses already processed data)\n", "- Proper coordinate system alignment\n", "- Minimal but effective visualization"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Papermill parameters\n", "ground_method = \"csf\"\n", "site_name = \"trino_enel\"\n", "max_iterations = 30\n", "tolerance = 1e-6\n", "output_dir = \"../../data/output_runs/alignment_testing\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import open3d as o3d\n", "import matplotlib.pyplot as plt\n", "import time\n", "from pathlib import Path\n", "from scipy.spatial import cKDTree\n", "\n", "np.random.seed(42)\n", "output_path = Path(output_dir) / ground_method\n", "output_path.mkdir(parents=True, exist_ok=True)\n", "\n", "print(f\"ICP Alignment (Fixed) - {ground_method.upper()}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load already processed point clouds (no additional downsampling)\n", "source_file = Path(f\"../../data/processed/{site_name}/ground_segmentation/{ground_method}/trino_enel_nonground.ply\")\n", "target_file = Path(f\"../../data/processed/{site_name}/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\")\n", "\n", "print(f\"Loading point clouds...\")\n", "print(f\"Source exists: {source_file.exists()}\")\n", "print(f\"Target exists: {target_file.exists()}\")\n", "\n", "if source_file.exists() and target_file.exists():\n", "    source_pcd = o3d.io.read_point_cloud(str(source_file))\n", "    target_pcd = o3d.io.read_point_cloud(str(target_file))\n", "    \n", "    source_points = np.asarray(source_pcd.points)\n", "    target_points = np.asarray(target_pcd.points)\n", "    \n", "    print(f\"Source: {source_points.shape[0]:,} points\")\n", "    print(f\"Target: {target_points.shape[0]:,} points\")\n", "    \n", "    # Check coordinate systems\n", "    source_centroid = np.mean(source_points, axis=0)\n", "    target_centroid = np.mean(target_points, axis=0)\n", "    \n", "    print(f\"Source centroid: [{source_centroid[0]:.1f}, {source_centroid[1]:.1f}, {source_centroid[2]:.1f}]\")\n", "    print(f\"Target centroid: [{target_centroid[0]:.1f}, {target_centroid[1]:.1f}, {target_centroid[2]:.1f}]\")\n", "    print(f\"Distance: {np.linalg.norm(source_centroid - target_centroid):.1f} m\")\n", "    \n", "else:\n", "    print(\"ERROR: Could not load point clouds!\")\n", "    # Create dummy data\n", "    source_points = np.random.randn(1000, 3) * 10\n", "    target_points = np.random.randn(800, 3) * 10 + [5, 5, 0]\n", "    source_centroid = np.mean(source_points, axis=0)\n", "    target_centroid = np.mean(target_points, axis=0)\n", "    print(f\"Using dummy data\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Center both point clouds at origin\n", "print(\"\\nAligning coordinate systems...\")\n", "\n", "source_centered = source_points - source_centroid\n", "target_centered = target_points - target_centroid\n", "\n", "print(f\"Centered both point clouds at origin\")\n", "\n", "# Check scale\n", "source_scale = np.std(np.linalg.norm(source_centered, axis=1))\n", "target_scale = np.std(np.linalg.norm(target_centered, axis=1))\n", "scale_ratio = target_scale / source_scale if source_scale > 0 else 1.0\n", "\n", "print(f\"Scale ratio: {scale_ratio:.3f}\")\n", "\n", "# Apply scale if needed\n", "if abs(scale_ratio - 1.0) > 0.2:\n", "    print(f\"Applying scale normalization: {scale_ratio:.3f}\")\n", "    source_centered *= scale_ratio\n", "    scale_applied = scale_ratio\n", "else:\n", "    scale_applied = 1.0\n", "    print(\"No scale normalization needed\")\n", "\n", "# Downsample for ICP computation only\n", "max_points = 3000\n", "if len(source_centered) > max_points:\n", "    source_idx = np.random.choice(len(source_centered), max_points, replace=False)\n", "    source_work = source_centered[source_idx]\n", "else:\n", "    source_work = source_centered\n", "\n", "if len(target_centered) > max_points:\n", "    target_idx = np.random.choice(len(target_centered), max_points, replace=False)\n", "    target_work = target_centered[target_idx]\n", "else:\n", "    target_work = target_centered\n", "\n", "print(f\"Working sets: source={source_work.shape[0]:,}, target={target_work.shape[0]:,}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Simple ICP implementation\n", "def simple_icp(source, target, max_iter=30, tolerance=1e-6):\n", "    source_current = source.copy()\n", "    prev_error = float('inf')\n", "    errors = []\n", "    T_total = np.eye(4)\n", "    \n", "    start_time = time.time()\n", "    \n", "    for i in range(max_iter):\n", "        # Find closest points\n", "        tree = cKDTree(target)\n", "        distances, indices = tree.query(source_current)\n", "        \n", "        # Calculate error\n", "        mse = np.mean(distances**2)\n", "        rmse = np.sqrt(mse)\n", "        errors.append(mse)\n", "        \n", "        print(f\"Iter {i+1:2d}: RMSE = {rmse:.3f}m\")\n", "        \n", "        # Check convergence\n", "        if i > 0 and abs(prev_error - mse) < tolerance:\n", "            print(f\"Converged after {i+1} iterations\")\n", "            break\n", "        prev_error = mse\n", "        \n", "        # Get corresponding points\n", "        target_matched = target[indices]\n", "        \n", "        # Calculate centroids\n", "        source_mean = np.mean(source_current, axis=0)\n", "        target_mean = np.mean(target_matched, axis=0)\n", "        \n", "        # Center points\n", "        source_centered_iter = source_current - source_mean\n", "        target_centered_iter = target_matched - target_mean\n", "        \n", "        # Calculate rotation using SVD\n", "        H = np.dot(source_centered_iter.T, target_centered_iter)\n", "        U, S, Vt = np.linalg.svd(H)\n", "        R = np.dot(Vt.T, U.T)\n", "        \n", "        # Ensure proper rotation\n", "        if np.linalg.det(R) < 0:\n", "            Vt[-1, :] *= -1\n", "            R = np.dot(Vt.T, U.T)\n", "        \n", "        # Calculate translation\n", "        t = target_mean - np.dot(R, source_mean)\n", "        \n", "        # Apply transformation\n", "        source_current = np.dot(source_current, R.T) + t\n", "        \n", "        # Update total transformation\n", "        T = np.eye(4)\n", "        T[:3, :3] = R\n", "        T[:3, 3] = t\n", "        T_total = np.dot(T, T_total)\n", "    \n", "    elapsed = time.time() - start_time\n", "    return T_total, source_current, rmse, i+1, errors, elapsed\n", "\n", "# Run ICP\n", "print(\"\\nRunning ICP...\")\n", "T_icp, aligned_source, final_rmse, iterations, convergence_history, icp_time = simple_icp(\n", "    source_work, target_work, max_iterations, tolerance\n", ")\n", "\n", "print(f\"\\nICP completed in {icp_time:.2f}s\")\n", "print(f\"Final RMSE: {final_rmse:.3f}m\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Apply transformation to full resolution\n", "print(\"\\nApplying to full resolution...\")\n", "\n", "# Create combined transformation\n", "T_coord = np.eye(4)\n", "T_coord[:3, :3] = np.eye(3) * scale_applied\n", "T_coord[:3, 3] = target_centroid - source_centroid * scale_applied\n", "\n", "T_combined = np.dot(T_icp, T_coord)\n", "\n", "# Apply to full resolution\n", "R_combined = T_combined[:3, :3]\n", "t_combined = T_combined[:3, 3]\n", "aligned_full = np.dot(source_points, R_combined.T) + t_combined\n", "\n", "print(f\"Transformed {aligned_full.shape[0]:,} points\")\n", "\n", "# Calculate metrics\n", "tree = cKDTree(target_points)\n", "distances_before, _ = tree.query(source_points)\n", "distances_after, _ = tree.query(aligned_full)\n", "\n", "rmse_before = np.sqrt(np.mean(distances_before**2))\n", "rmse_after = np.sqrt(np.mean(distances_after**2))\n", "\n", "print(f\"\\nResults:\")\n", "print(f\"RMSE Before: {rmse_before:.2f} m\")\n", "print(f\"RMSE After:  {rmse_after:.2f} m\")\n", "print(f\"Improvement: {rmse_before - rmse_after:.2f} m ({((rmse_before - rmse_after)/rmse_before*100):.1f}%)\")\n", "\n", "# Accuracy\n", "acc_1cm = np.mean(distances_after < 0.01) * 100\n", "acc_5cm = np.mean(distances_after < 0.05) * 100\n", "acc_10cm = np.mean(distances_after < 0.1) * 100\n", "acc_1m = np.mean(distances_after < 1.0) * 100\n", "\n", "print(f\"\\nAccuracy:\")\n", "print(f\"< 1cm:  {acc_1cm:.1f}%\")\n", "print(f\"< 5cm:  {acc_5cm:.1f}%\")\n", "print(f\"< 10cm: {acc_10cm:.1f}%\")\n", "print(f\"< 1m:   {acc_1m:.1f}%\")\n", "\n", "# Quality\n", "if rmse_after < 1.0:\n", "    quality = \"EXCELLENT\"\n", "elif rmse_after < 5.0:\n", "    quality = \"GOOD\"\n", "elif rmse_after < 20.0:\n", "    quality = \"FAIR\"\n", "else:\n", "    quality = \"POOR\"\n", "\n", "print(f\"\\nQuality: {quality}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Simple before/after visualization\n", "print(\"\\nCreating visualization...\")\n", "\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))\n", "\n", "# Downsample for visualization\n", "n_vis = min(1000, len(source_points))\n", "source_idx = np.random.choice(len(source_points), n_vis, replace=False)\n", "target_idx = np.random.choice(len(target_points), min(1000, len(target_points)), replace=False)\n", "aligned_idx = np.random.choice(len(aligned_full), n_vis, replace=False)\n", "\n", "# Before alignment\n", "ax1.scatter(source_points[source_idx, 0], source_points[source_idx, 1], \n", "           c='red', s=1, alpha=0.6, label='Source')\n", "ax1.scatter(target_points[target_idx, 0], target_points[target_idx, 1], \n", "           c='green', s=1, alpha=0.6, label='Target')\n", "ax1.set_title(f'Before\\nRMSE: {rmse_before:.2f}m')\n", "ax1.legend()\n", "ax1.grid(True, alpha=0.3)\n", "ax1.set_aspect('equal')\n", "\n", "# After alignment\n", "ax2.scatter(aligned_full[aligned_idx, 0], aligned_full[aligned_idx, 1], \n", "           c='blue', s=1, alpha=0.6, label='Aligned')\n", "ax2.scatter(target_points[target_idx, 0], target_points[target_idx, 1], \n", "           c='green', s=1, alpha=0.6, label='Target')\n", "ax2.set_title(f'After\\nRMSE: {rmse_after:.2f}m')\n", "ax2.legend()\n", "ax2.grid(True, alpha=0.3)\n", "ax2.set_aspect('equal')\n", "\n", "plt.suptitle(f'ICP Alignment - {ground_method.upper()} - {quality}', fontweight='bold')\n", "plt.tight_layout()\n", "\n", "# Save\n", "viz_path = output_path / f'icp_fixed_results_{ground_method}.png'\n", "plt.savefig(viz_path, dpi=150, bbox_inches='tight')\n", "print(f\"Saved: {viz_path}\")\n", "plt.show()\n", "\n", "# Save aligned point cloud\n", "aligned_pcd = o3d.geometry.PointCloud()\n", "aligned_pcd.points = o3d.utility.Vector3dVector(aligned_full)\n", "aligned_file = output_path / f'icp_fixed_aligned_{ground_method}.ply'\n", "o3d.io.write_point_cloud(str(aligned_file), aligned_pcd)\n", "\n", "print(f\"\\nSUMMARY:\")\n", "print(f\"Ground Method: {ground_method.upper()}\")\n", "print(f\"RMSE: {rmse_after:.2f}m\")\n", "print(f\"Quality: {quality}\")\n", "print(f\"Time: {icp_time:.1f}s\")\n", "print(f\"Files: {aligned_file.name}, {viz_path.name}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}