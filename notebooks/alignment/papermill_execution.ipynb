{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Papermill Alignment Testing Execution\n", "\n", "This notebook contains individual Papermill commands for systematically testing all ground segmentation methods against alignment algorithms. Each test is in a separate cell for easy individual execution or batch processing.\n", "\n", "**Testing Matrix**: 4 ground methods × 3 alignment notebooks = 12 tests  \n", "**Ground Methods**: CSF, PMF, RANSAC, RANSAC+PMF  \n", "**Alignment Methods**: ICP, Neural Network, Hybrid  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup and Preparation"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["# Install papermill if needed\n", "#! pip install papermill "]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Output directories created at: ../../data/output_runs/alignment_testing\n", "Testing will begin at: 2025-07-05 17:52:47\n", "\n", "Directory structure:\n", "  ../../data/output_runs/alignment_testing/csf\n", "  ../../data/output_runs/alignment_testing/pmf\n", "  ../../data/output_runs/alignment_testing/ransac\n", "  ../../data/output_runs/alignment_testing/ransac_pmf\n"]}], "source": ["# Setup environment and create output directories\n", "import os\n", "from pathlib import Path\n", "import subprocess\n", "from datetime import datetime\n", "\n", "# Create output directory structure\n", "output_base = Path(\"../../data/output_runs/alignment_testing\")\n", "ground_methods = [\"csf\", \"pmf\", \"ransac\", \"ransac_pmf\"]\n", "\n", "for method in ground_methods:\n", "    method_dir = output_base / method\n", "    method_dir.mkdir(parents=True, exist_ok=True)\n", "    (method_dir / \"results\").mkdir(exist_ok=True)\n", "\n", "print(f\"Output directories created at: {output_base}\")\n", "print(f\"Testing will begin at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n", "print(\"\\nDirectory structure:\")\n", "for method in ground_methods:\n", "    print(f\"  {output_base / method}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## CSF Ground Segmentation Tests"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### CSF + ICP Alignment"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Input Notebook:  01_icp_alignment.ipynb\n", "Output Notebook: ../../data/output_runs/alignment_testing/csf/01_icp_alignment_csf.ipynb\n", "Traceback (most recent call last):\n", "  File \"/Users/<USER>/.local/lib/python3.11/site-packages/nbformat/reader.py\", line 19, in parse_json\n", "    nb_dict = json.loads(s, **kwargs)\n", "              ^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/json/__init__.py\", line 346, in loads\n", "    return _default_decoder.decode(s)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/json/decoder.py\", line 340, in decode\n", "    raise JSONDecodeError(\"Extra data\", s, end)\n", "json.decoder.JSONDecodeError: Extra data: line 547 column 3 (char 19278)\n", "\n", "The above exception was the direct cause of the following exception:\n", "\n", "Traceback (most recent call last):\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/bin/papermill\", line 8, in <module>\n", "    sys.exit(papermill())\n", "             ^^^^^^^^^^^\n", "  File \"/Users/<USER>/.local/lib/python3.11/site-packages/click/core.py\", line 1442, in __call__\n", "    return self.main(*args, **kwargs)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/Users/<USER>/.local/lib/python3.11/site-packages/click/core.py\", line 1363, in main\n", "    rv = self.invoke(ctx)\n", "         ^^^^^^^^^^^^^^^^\n", "  File \"/Users/<USER>/.local/lib/python3.11/site-packages/click/core.py\", line 1226, in invoke\n", "    return ctx.invoke(self.callback, **ctx.params)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/Users/<USER>/.local/lib/python3.11/site-packages/click/core.py\", line 794, in invoke\n", "    return callback(*args, **kwargs)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/Users/<USER>/.local/lib/python3.11/site-packages/click/decorators.py\", line 34, in new_func\n", "    return f(get_current_context(), *args, **kwargs)\n", "           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/papermill/cli.py\", line 235, in papermill\n", "    execute_notebook(\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/papermill/execute.py\", line 89, in execute_notebook\n", "    nb = load_notebook_node(input_path)\n", "         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/papermill/iorw.py\", line 499, in load_notebook_node\n", "    nb = nbformat.reads(papermill_io.read(notebook_path), as_version=4)\n", "         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/Users/<USER>/.local/lib/python3.11/site-packages/nbformat/__init__.py\", line 92, in reads\n", "    nb = reader.reads(s, **kwargs)\n", "         ^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/Users/<USER>/.local/lib/python3.11/site-packages/nbformat/reader.py\", line 75, in reads\n", "    nb_dict = parse_json(s, **kwargs)\n", "              ^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/Users/<USER>/.local/lib/python3.11/site-packages/nbformat/reader.py\", line 25, in parse_json\n", "    raise NotJSONError(message) from e\n", "nbformat.reader.NotJSONError: Notebook does not appear to be JSON: '{\\n \"cells\": [\\n  {\\n   \"cell_type\": \"m...\n"]}], "source": ["# CSF + ICP Alignment Test\n", "!papermill 01_icp_alignment.ipynb \\\n", "    ../../data/output_runs/alignment_testing/csf/01_icp_alignment_csf.ipynb \\\n", "    -p ground_method \"csf\" \\\n", "    -p site_name \"trino_enel\" \\\n", "    -p project_type \"trino_enel\" \\\n", "    --log-output \\\n", "    --kernel pytorch-geo-dev\n"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Input Notebook:  01_icp_alignment.ipynb\n", "Output Notebook: 01_icp_alignment_output.ipynb\n", "Unable to parse line 6 'voxel_size = 0.02  # 0 = no downsampling'.\n", "Passed unknown parameter: voxel_size\n", "Executing notebook with kernel: pytorch-geo-dev\n", "Executing Cell 1---------------------------------------\n", "Ending Cell 1------------------------------------------\n", "Executing Cell 2---------------------------------------\n", "Ending Cell 2------------------------------------------\n", "Executing Cell 3---------------------------------------\n", "Ending Cell 3------------------------------------------\n", "Executing Cell 4---------------------------------------\n", "No handler found for comm target 'dash'\n", "ICP Alignment Exploration - CSF\n", "Output: ../../data/output_runs/alignment_testing/csf\n", "\n", "Ending Cell 4------------------------------------------\n", "Executing Cell 5---------------------------------------\n", "Ending Cell 5------------------------------------------\n", "Executing Cell 6---------------------------------------\n", "Source: ../../data/processed/trino_enel/ground_segmentation/csf/trino_enel_nonground.ply\n", "Target: ../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\n", "Source exists: True\n", "Target exists: True\n", "\n", "Ending Cell 6------------------------------------------\n", "Executing Cell 7---------------------------------------\n", "Loaded source: 13,848 points\n", "Loaded target: 118,920 points\n", "\n", "Source bounds: X[435223.7, 436794.1] Y[5010816.9, 5012539.1] Z[1.2, 13.1]\n", "Target bounds: X[435267.2, 436720.0] Y[5010900.7, 5012462.4] Z[152.8, 161.7]\n", "\n", "Ending Cell 7------------------------------------------\n", "Executing Cell 8---------------------------------------\n", "Ending Cell 8------------------------------------------\n", "Executing Cell 9---------------------------------------\n", "Downsampling with voxel size: 0.05\n", "After downsampling: source=13,842, target=118,908\n", "\n", "Ending Cell 9------------------------------------------\n", "Executing Cell 10--------------------------------------\n", "\n", "Pre-aligning coordinate systems...\n", "Source centroid: [436024.6, 5011683.2, 2.5]\n", "Target centroid: [435986.3, 5011746.8, 157.3]\n", "Scale ratio (target/source): 0.972\n", "No scale normalization needed\n", "Pre-alignment complete. Scale factor: 1.000\n", "\n", "Ending Cell 10-----------------------------------------\n", "Executing Cell 11--------------------------------------\n", "Ending Cell 11-----------------------------------------\n", "Executing Cell 12--------------------------------------\n", "\n", "Running ICP alignment...\n", "Iter  1: MSE = 2.329512e+03\n", "Iter  2: MSE = 1.825019e+03\n", "\n", "Iter  3: MSE = 1.528635e+03\n", "\n", "Iter  4: MSE = 1.370685e+03\n", "\n", "Iter  5: MSE = 1.284943e+03\n", "Iter  6: MSE = 1.238470e+03\n", "Iter  7: MSE = 1.212420e+03\n", "\n", "Iter  8: MSE = 1.198702e+03\n", "\n", "Iter  9: MSE = 1.190795e+03\n", "\n", "Iter 10: MSE = 1.185570e+03\n", "Iter 11: MSE = 1.182568e+03\n", "Iter 12: MSE = 1.180911e+03\n", "\n", "Iter 13: MSE = 1.180034e+03\n", "\n", "Iter 14: MSE = 1.179549e+03\n", "\n", "Iter 15: MSE = 1.179209e+03\n", "Iter 16: MSE = 1.178977e+03\n", "Iter 17: MSE = 1.178834e+03\n", "\n", "Iter 18: MSE = 1.178765e+03\n", "\n", "Iter 19: MSE = 1.178734e+03\n", "\n", "Iter 20: MSE = 1.178713e+03\n", "Iter 21: MSE = 1.178698e+03\n", "Iter 22: MSE = 1.178687e+03\n", "\n", "Iter 23: MSE = 1.178679e+03\n", "\n", "Iter 24: MSE = 1.178673e+03\n", "\n", "Iter 25: MSE = 1.178666e+03\n", "Iter 26: MSE = 1.178654e+03\n", "Iter 27: MSE = 1.178630e+03\n", "\n", "Iter 28: MSE = 1.178586e+03\n", "\n", "Iter 29: MSE = 1.178518e+03\n", "\n", "Iter 30: MSE = 1.178447e+03\n", "\n", "ICP completed in 1.31s\n", "Iterations: 30\n", "Final MSE: 1.178447e+03\n", "\n", "Ending Cell 12-----------------------------------------\n"]}], "source": ["!papermill 01_icp_alignment.ipynb \\\n", "  01_icp_alignment_output.ipynb \\\n", "  -p ground_method \"csf\" \\\n", "  -p max_iterations 30 \\\n", "  -p voxel_size 0.05 \\\n", "  --log-output \\\n", "  --kernel pytorch-geo-dev\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["### CSF + Neural Network Alignment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# CSF + Neural Network Alignment Test\n", "!papermill 02_neural_network_alignment.ipynb \\\n", "    ../../data/output_runs/alignment_testing/csf/02_neural_network_alignment_csf.ipynb \\\n", "    -p ground_method \"csf\" \\\n", "    -p site_name \"trino_enel\" \\\n", "    -p project_type \"trino_enel\" \\\n", "    --log-output \\\n", "    --kernel pytorch-geo-dev"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Input Notebook:  01_pile_anchor_alignment.ipynb\n", "Output Notebook: pile_output.ipynb\n", "Executing notebook with kernel: pytorch-geo-dev\n", "Executing Cell 1---------------------------------------\n", "Ending Cell 1------------------------------------------\n", "Executing Cell 2---------------------------------------\n", "Ending Cell 2------------------------------------------\n", "Executing Cell 3---------------------------------------\n", "Ending Cell 3------------------------------------------\n", "Executing Cell 4---------------------------------------\n", "No handler found for comm target 'dash'\n", "🏗️ PILE-BASED ANCHOR POINT ALIGNMENT\n", "Site: trino_enel\n", "Ground Method: csf\n", "Output: ../../data/output_runs/pile_anchor_alignment/csf\n", "\n", "Ending Cell 4------------------------------------------\n", "Executing Cell 5---------------------------------------\n", "Ending Cell 5------------------------------------------\n", "Executing Cell 6---------------------------------------\n", "\n", "🔍 Extracting pile coordinates from IFC...\n", "❌ IFC file not found: ../../data/processed/trino_enel/ifc_files/GRE.EEC.S.00.IT.P.14353.00.265.ifc\n", "\n", "Ending Cell 6------------------------------------------\n", "Executing Cell 7---------------------------------------\n", "Ending Cell 7------------------------------------------\n", "Executing Cell 8---------------------------------------\n", "\n", "📁 Loading point cloud...\n", "Source: True - ../../data/processed/trino_enel/ground_segmentation/csf/trino_enel_nonground.ply\n", "✅ Loaded 13,848 points\n", "   X range: 435223.72 to 436794.15\n", "   Y range: 5010816.92 to 5012539.06\n", "   Z range: 1.17 to 13.14\n", "   Estimated ground level: 1.32m\n", "\n", "Ending Cell 8------------------------------------------\n", "Executing Cell 9---------------------------------------\n", "\n", "🔍 Detecting pile locations in point cloud...\n", "   Points above ground (1.0m-15.0m): 7,544\n", "   Found 10 potential pile locations\n", "\n", "Ending Cell 9------------------------------------------\n", "Executing Cell 10--------------------------------------\n", "Ending Cell 10-----------------------------------------\n", "Executing Cell 11--------------------------------------\n", "\n", "⚠️  Cannot proceed with pile-based alignment:\n", "   - No pile coordinates extracted from IFC\n", "\n", "Ending Cell 11-----------------------------------------\n", "Executing Cell 12--------------------------------------\n", "Ending Cell 12-----------------------------------------\n", "Executing Cell 13--------------------------------------\n", "\n", "❌ Cannot calculate transformation - no correspondences found\n", "\n", "Ending Cell 13-----------------------------------------\n", "Executing Cell 14--------------------------------------\n", "Ending Cell 14-----------------------------------------\n", "Executing Cell 15--------------------------------------\n", "\n", "❌ Cannot complete pile-based alignment\n", "   Check IFC file location and pile detection parameters\n", "\n", "Ending Cell 15-----------------------------------------\n"]}], "source": ["!papermill 01_pile_anchor_alignment.ipynb pile_output.ipynb -p ground_method \"csf\" --log-output --kernel pytorch-geo-dev"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### CSF + Hybrid Alignment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# CSF + Hybrid Alignment Test\n", "!papermill 03_hybrid_alignment.ipynb \\\n", "    ../../data/output_runs/alignment_testing/csf/03_hybrid_alignment_csf.ipynb \\\n", "    -p ground_method \"csf\" \\\n", "    -p site_name \"trino_enel\" \\\n", "    -p project_type \"trino_enel\" \\\n", "    --log-output \\\n", "    --kernel pytorch-geo-dev"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Input Notebook:  00_alignment_diagnostics.ipynb\n", "Output Notebook: diagnostic_output.ipynb\n", "Executing notebook with kernel: pytorch-geo-dev\n", "Executing Cell 1---------------------------------------\n", "Ending Cell 1------------------------------------------\n", "Executing Cell 2---------------------------------------\n", "Ending Cell 2------------------------------------------\n", "Executing Cell 3---------------------------------------\n", "Ending Cell 3------------------------------------------\n", "Executing Cell 4---------------------------------------\n", "No handler found for comm target 'dash'\n", "🔍 ALIGNMENT DIAGNOSTICS & IFC METADATA ANALYSIS\n", "Site: trino_enel\n", "Ground Method: csf\n", "Output: ../../data/output_runs/alignment_diagnostics\n", "\n", "Ending Cell 4------------------------------------------\n", "Executing Cell 5---------------------------------------\n", "Ending Cell 5------------------------------------------\n", "Executing Cell 6---------------------------------------\n", "📁 Loading point clouds...\n", "Source (LAS): True - ../../data/processed/trino_enel/ground_segmentation/csf/trino_enel_nonground.ply\n", "Target (IFC): True - ../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\n", "✅ Loaded successfully\n", "   Source: 13,848 points\n", "   Target: 118,920 points\n", "\n", "Ending Cell 6------------------------------------------\n", "Executing Cell 7---------------------------------------\n", "Ending Cell 7------------------------------------------\n", "Executing Cell 8---------------------------------------\n", "\n", "📊 POINT CLOUD ANALYSIS\n", "==================================================\n", "\n", "🗺️  COORDINATE RANGES:\n", "Source (LAS/Ground Segmented):\n", "   X: 435223.72 to 436794.15 (range: 1570.42m)\n", "   Y: 5010816.92 to 5012539.06 (range: 1722.14m)\n", "   Z: 1.17 to 13.14 (range: 11.96m)\n", "\n", "Target (IFC Point Cloud):\n", "   X: 435267.17 to 436719.98 (range: 1452.81m)\n", "   Y: 5010900.69 to 5012462.43 (range: 1561.75m)\n", "   Z: 152.85 to 161.66 (range: 8.81m)\n", "\n", "🎯 CENTROIDS:\n", "Source: [436024.70, 5011683.28, 2.46]\n", "Target: [435986.31, 5011746.74, 157.35]\n", "Distance between centroids: 171.73m\n", "\n", "📐 OFFSET (Target - Source):\n", "   ΔX: -38.39m\n", "   ΔY: 63.46m\n", "   ΔZ: 154.88m\n", "\n", "📏 SCALE ANALYSIS:\n", "Source scale (std): 180.53m\n", "Target scale (std): 168.58m\n", "Scale ratio: 0.934\n", "\n", "🔢 DENSITY ANALYSIS:\n", "Source density: 0.01 points/m²\n", "Target density: 0.05 points/m²\n", "\n", "Ending Cell 8------------------------------------------\n", "Executing Cell 9---------------------------------------\n", "Ending Cell 9------------------------------------------\n", "Executing Cell 10--------------------------------------\n", "\n", "🏗️  IFC METADATA EXTRACTION\n", "==================================================\n", "IFC File: False - ../../data/processed/trino_enel/ifc_files/GRE.EEC.S.00.IT.P.14353.00.265.ifc\n", "❌ IFC file not found: ../../data/processed/trino_enel/ifc_files/GRE.EEC.S.00.IT.P.14353.00.265.ifc\n", "\n", "Ending Cell 10-----------------------------------------\n", "Executing Cell 11--------------------------------------\n", "Ending Cell 11-----------------------------------------\n", "Executing Cell 12--------------------------------------\n", "\n", "📊 CREATING VISUAL COMPARISON\n", "==================================================\n", "\n", "📊 Visualization saved: ../../data/output_runs/alignment_diagnostics/alignment_diagnostics_csf.png\n", "\n", "<Figure size 1600x1200 with 6 Axes>\n", "Ending Cell 12-----------------------------------------\n", "Executing Cell 13--------------------------------------\n", "Ending Cell 13-----------------------------------------\n", "Executing Cell 14--------------------------------------\n", "\n", "🔍 DIAGNOSIS & RECOMMENDATIONS\n", "==================================================\n", "\n", "🎯 ISSUE ANALYSIS:\n", "⚠️  SIGNIFICANT COORDINATE OFFSET\n", "   Centroid distance: 171.7m (100-1000m indicates offset or different origins)\n", "❌ MAJOR Z-OFFSET ISSUE\n", "   Z offset: 154.9m (>100m indicates different vertical datums)\n", "✅ SIMILAR SCALES\n", "   Scale ratio: 0.934 (<10% difference is acceptable)\n", "\n", "💡 RECOMMENDATIONS:\n", "\n", "🔧 COORDINATE SYSTEM FIXES (HIGH PRIORITY):\n", "   2. Manual coordinate system alignment before ICP\n", "   3. Check LAS file coordinate system metadata\n", "   4. Verify IFC model coordinate system\n", "\n", "🎯 ALIGNMENT STRATEGY:\n", "   1. COORDINATE TRANSFORMATION FIRST\n", "      - Apply IFC coordinate offsets\n", "      - Center both point clouds\n", "      - Apply scale normalization\n", "   2. THEN RUN ICP\n", "      - Use smaller point sets (1000-3000 points)\n", "      - Increase tolerance if needed\n", "      - Consider robust ICP variants\n", "\n", "🔄 ALTERNATIVE APPROACHES:\n", "   1. Feature-based alignment using distinctive structures\n", "   2. Manual correspondence points selection\n", "   3. Multi-scale ICP (coarse to fine)\n", "   4. <PERSON><PERSON> with outlier rejection\n", "\n", "💾 DIAGNOSTIC REPORT SAVED: ../../data/output_runs/alignment_diagnostics/diagnostic_report_csf.json\n", "\n", "✅ DIAGNOSTICS COMPLETE!\n", "\n", "Next steps:\n", "1. Review the visualization and coordinate analysis\n", "2. Apply recommended coordinate transformations\n", "3. Re-run ICP alignment with fixes\n", "4. Consider alternative alignment methods if needed\n", "\n", "Ending Cell 14-----------------------------------------\n"]}], "source": ["!papermill 00_alignment_diagnostics.ipynb diagnostic_output.ipynb -p ground_method \"csf\" --log-output --kernel pytorch-geo-dev"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. <PERSON><PERSON><PERSON> Metadata Extraction Results"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## PMF Ground Segmentation Tests"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### PMF + ICP Alignment"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Input Notebook:  01_icp_alignment.ipynb\n", "Output Notebook: ../../data/output_runs/alignment_testing/pmf/01_icp_alignment_pmf.ipynb\n", "Executing notebook with kernel: pytorch-geo-dev\n", "Executing Cell 1---------------------------------------\n", "Ending Cell 1------------------------------------------\n", "Executing Cell 2---------------------------------------\n", "INFO:__main__:Configuration:\n", "\n", "INFO:__main__:  Ground Method: csf\n", "\n", "INFO:__main__:  Source File: ../../data/processed/trino_enel/ground_segmentation/csf/trino_enel_nonground.ply\n", "\n", "INFO:__main__:  Target File: GRE.EEC.S.00.IT.P.14353.00.265_pointcloud.ply\n", "\n", "INFO:__main__:  Output Dir: ../../data/output_runs/alignment_testing/csf\n", "\n", "INFO:__main__:  MLflow Run: icp_csf_trino_enel\n", "\n", "INFO:__main__:ICP Configuration:\n", "\n", "INFO:__main__:  Max Iterations: 50\n", "\n", "INFO:__main__:  Tolerance: 1e-06\n", "\n", "INFO:__main__:  Distance Threshold: 0.1\n", "\n", "Ending Cell 2------------------------------------------\n", "Executing Cell 3---------------------------------------\n", "Ending Cell 3------------------------------------------\n", "Executing Cell 4---------------------------------------\n", "Ending Cell 4------------------------------------------\n", "Executing Cell 5---------------------------------------\n", "INFO:__main__:File Validation for pmf:\n", "\n", "INFO:__main__:  Source: ../../data/processed/trino_enel/ground_segmentation/csf/trino_enel_nonground.ply\n", "\n", "INFO:__main__:  Target: ../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_pointcloud.ply\n", "\n", "INFO:__main__:File validation passed - ready to proceed\n", "\n", "INFO:__main__:Final source file: ../../data/processed/trino_enel/ground_segmentation/csf/trino_enel_nonground.ply\n", "\n", "INFO:__main__:Final target file: ../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_pointcloud.ply\n", "\n", "Ending Cell 5------------------------------------------\n", "Executing Cell 6---------------------------------------\n", "Ending Cell 6------------------------------------------\n", "Executing Cell 7---------------------------------------\n", "Ending Cell 7------------------------------------------\n", "Executing Cell 8---------------------------------------\n", "Ending Cell 8------------------------------------------\n", "Executing Cell 9---------------------------------------\n", "No handler found for comm target 'dash'\n", "INFO:__main__:ICP Alignment Environment Initialized\n", "\n", "INFO:__main__:Open3D version: 0.19.0\n", "\n", "INFO:__main__:Analysis Date: 2025-07-04 21:32:40\n", "\n", "Ending Cell 9------------------------------------------\n", "Executing Cell 10--------------------------------------\n", "Ending Cell 10-----------------------------------------\n", "Executing Cell 11--------------------------------------\n", "INFO:__main__:Project: trino_enel/trino_enel\n", "\n", "INFO:__main__:Input path: ../../data/trino_enel/trino_enel/preprocessing\n", "\n", "INFO:__main__:Output path: ../../data/output_runs/alignment_testing/csf/trino_enel/trino_enel/icp_results\n", "\n", "INFO:__main__:Source file: ../../data/processed/trino_enel/ground_segmentation/csf/trino_enel_nonground.ply\n", "\n", "INFO:__main__:Target file: ../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_pointcloud.ply\n", "\n", "INFO:__main__:MLflow experiment initialized\n", "\n", "Ending Cell 11-----------------------------------------\n", "Executing Cell 12--------------------------------------\n", "Ending Cell 12-----------------------------------------\n", "Executing Cell 13--------------------------------------\n", "Ending Cell 13-----------------------------------------\n", "Executing Cell 14--------------------------------------\n", "Ending Cell 14-----------------------------------------\n", "Executing Cell 15--------------------------------------\n", "Ending Cell 15-----------------------------------------\n", "Executing Cell 16--------------------------------------\n", "Ending Cell 16-----------------------------------------\n", "Executing Cell 17--------------------------------------\n", "Ending Cell 17-----------------------------------------\n", "Executing Cell 18--------------------------------------\n", "Ending Cell 18-----------------------------------------\n", "Executing Cell 19--------------------------------------\n", "Ending Cell 19-----------------------------------------\n", "Executing Cell 20--------------------------------------\n", "Ending Cell 20-----------------------------------------\n", "Executing Cell 21--------------------------------------\n", "Ending Cell 21-----------------------------------------\n", "Executing Cell 22--------------------------------------\n", "Ending Cell 22-----------------------------------------\n", "Executing Cell 23--------------------------------------\n", "total 9776\n", "drwxr-xr-x@ 3 <USER>  <GROUP>    96B Jul  4 20:14 \u001b[34mGRE.EEC.S.00.IT.P.14353.00.265\u001b[m\u001b[m\n", "-rw-r--r--@ 1 <USER>  <GROUP>   4.8M Jul  4 20:15 GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\n", "-rw-r--r--@ 1 <USER>  <GROUP>   1.1K Jul  4 20:15 GRE.EEC.S.00.IT.P.14353.00.265_data_driven.report.txt\n", "-rw-r--r--@ 1 <USER>  <GROUP>   923B Jul  4 20:15 GRE.EEC.S.00.IT.P.14353.00.265_data_driven.strategy.json\n", "drwxr-xr-x@ 2 <USER>  <GROUP>    64B Jul  2 13:18 \u001b[34mGRE.EEC.S.00.IT.P.14353.00.265_pointcloud.ply\u001b[m\u001b[m\n", "\n", "Ending Cell 23-----------------------------------------\n", "Executing Cell 24--------------------------------------\n", "INFO:__main__:Loading point cloud data...\n", "\n", "INFO:__main__:Loading source: ../../data/processed/trino_enel/ground_segmentation/pmf/trino_enel_nonground.ply\n", "\n", "INFO:__main__:Reading point cloud file: ../../data/processed/trino_enel/ground_segmentation/pmf/trino_enel_nonground.ply\n", "\n", "INFO:__main__:Loaded 36709 points from ../../data/processed/trino_enel/ground_segmentation/pmf/trino_enel_nonground.ply\n", "\n", "INFO:__main__:Loading target: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\n", "\n", "INFO:__main__:Reading point cloud file: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\n", "\n", "INFO:__main__:Loaded 118920 points from /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\n", "\n", "INFO:__main__:Loaded source shape: (36709, 3), target shape: (118920, 3)\n", "\n", "INFO:__main__:\n", "Preprocessing point clouds...\n", "\n", "INFO:__main__:Downsampling with voxel size: 0.02\n", "\n", "INFO:__main__:Downsampled source: 36709 points\n", "\n", "INFO:__main__:Downsampled target: 118916 points\n", "\n", "INFO:__main__:=== Executing ICP Alignment ===\n", "\n", "INFO:__main__:Iteration   1, MSE: 24541.3302458098\n", "\n", "INFO:__main__:Iteration   2, MSE: 850.7776108530\n", "\n", "INFO:__main__:Iteration   3, MSE: 836.5443182670\n", "\n", "INFO:__main__:Iteration   4, MSE: 825.9429882631\n", "\n", "INFO:__main__:Iteration   5, MSE: 817.9697239497\n", "\n", "INFO:__main__:Iteration   6, MSE: 812.3283398341\n", "\n", "INFO:__main__:Iteration   7, MSE: 808.6481595536\n", "\n", "INFO:__main__:Iteration   8, MSE: 805.9439233817\n", "\n", "INFO:__main__:Iteration   9, MSE: 804.0031912462\n", "\n", "INFO:__main__:Iteration  10, MSE: 802.5954620716\n", "\n", "INFO:__main__:Iteration  11, MSE: 801.6946325417\n", "\n", "INFO:__main__:Iteration  12, MSE: 801.1310042877\n", "\n", "INFO:__main__:Iteration  13, MSE: 800.7558546332\n", "\n", "INFO:__main__:Iteration  14, MSE: 800.5066119258\n", "\n", "INFO:__main__:Iteration  15, MSE: 800.3289614707\n", "\n", "INFO:__main__:Iteration  16, MSE: 800.2089670340\n", "\n", "INFO:__main__:Iteration  17, MSE: 800.1168564498\n", "\n", "INFO:__main__:Iteration  18, MSE: 800.0465821030\n", "\n", "INFO:__main__:Iteration  19, MSE: 799.9962210731\n", "\n", "INFO:__main__:Iteration  20, MSE: 799.9601443987\n", "\n", "INFO:__main__:Iteration  21, MSE: 799.9315211836\n", "\n", "INFO:__main__:Iteration  22, MSE: 799.9143547142\n", "\n", "INFO:__main__:Iteration  23, MSE: 799.9054236737\n", "\n", "INFO:__main__:Iteration  24, MSE: 799.8996821743\n", "\n", "INFO:__main__:Iteration  25, MSE: 799.8959131056\n", "\n", "INFO:__main__:Iteration  26, MSE: 799.8929602226\n", "\n", "INFO:__main__:Iteration  27, MSE: 799.8912228594\n", "\n", "INFO:__main__:Iteration  28, MSE: 799.8900134789\n", "\n", "INFO:__main__:Iteration  29, MSE: 799.8892406095\n", "\n", "INFO:__main__:Iteration  30, MSE: 799.8886660004\n", "\n", "INFO:__main__:Iteration  31, MSE: 799.8881602968\n", "\n", "INFO:__main__:Iteration  32, MSE: 799.8875509557\n", "\n", "INFO:__main__:Iteration  33, MSE: 799.8872182607\n", "\n", "INFO:__main__:Iteration  34, MSE: 799.8869742041\n", "\n", "INFO:__main__:Iteration  35, MSE: 799.8868074653\n", "\n", "INFO:__main__:Iteration  36, MSE: 799.8866296291\n", "\n", "INFO:__main__:Iteration  37, MSE: 799.8865115584\n", "\n", "INFO:__main__:Iteration  38, MSE: 799.8864437315\n", "\n", "INFO:__main__:Iteration  39, MSE: 799.8864174279\n", "\n", "INFO:__main__:Iteration  40, MSE: 799.8863844283\n", "\n", "INFO:__main__:Iteration  41, MSE: 799.8862743586\n", "\n", "INFO:__main__:Iteration  42, MSE: 799.8861505817\n", "\n", "INFO:__main__:Iteration  43, MSE: 799.8860816021\n", "\n", "INFO:__main__:Iteration  44, MSE: 799.8860111165\n", "\n", "INFO:__main__:Iteration  45, MSE: 799.8859780839\n", "\n", "INFO:__main__:Iteration  46, MSE: 799.8859701494\n", "\n", "INFO:__main__:Iteration  47, MSE: 799.8859568575\n", "\n", "INFO:__main__:Iteration  48, MSE: 799.8859276295\n", "\n", "INFO:__main__:Iteration  49, MSE: 799.8858921747\n", "\n", "INFO:__main__:Iteration  50, MSE: 799.8858741969\n", "\n", "INFO:__main__:ICP completed in 3.4898 seconds\n", "\n", "WARNING:__main__:Warning: Maximum iterations (50) reached without convergence.\n", "\n", "INFO:__main__:=== ICP Results ===\n", "\n", "INFO:__main__:Execution time: 3.4907 seconds\n", "\n", "INFO:__main__:Iterations: 50\n", "\n", "INFO:__main__:Final MSE: 799.8858741969\n", "\n", "INFO:__main__:Converged: False\n", "\n", "Ending Cell 24-----------------------------------------\n", "Executing Cell 25--------------------------------------\n", "INFO:__main__:Loading source: ../../data/processed/trino_enel/ground_segmentation/pmf/trino_enel_nonground.ply\n", "\n", "INFO:__main__:Loading target: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\n", "\n", "Ending Cell 25-----------------------------------------\n", "Executing Cell 26--------------------------------------\n", "INFO:__main__:Source has points: True, count: 36709\n", "\n", "INFO:__main__:Target has points: True, count: 118920\n", "\n", "--- Logging error ---\n", "Traceback (most recent call last):\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/logging/__init__.py\", line 1110, in emit\n", "    msg = self.format(record)\n", "          ^^^^^^^^^^^^^^^^^^^\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/logging/__init__.py\", line 953, in format\n", "    return fmt.format(record)\n", "           ^^^^^^^^^^^^^^^^^^\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/logging/__init__.py\", line 687, in format\n", "    record.message = record.getMessage()\n", "                     ^^^^^^^^^^^^^^^^^^^\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/logging/__init__.py\", line 377, in getMessage\n", "    msg = msg % self.args\n", "          ~~~~^~~~~~~~~~~\n", "TypeError: not all arguments converted during string formatting\n", "Call stack:\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/runpy.py\", line 198, in _run_module_as_main\n", "    return _run_code(code, main_globals, None,\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/runpy.py\", line 88, in _run_code\n", "    exec(code, run_globals)\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/ipykernel_launcher.py\", line 18, in <module>\n", "    app.launch_new_instance()\n", "  File \"/Users/<USER>/.local/lib/python3.11/site-packages/traitlets/config/application.py\", line 1075, in launch_instance\n", "    app.start()\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/ipykernel/kernelapp.py\", line 739, in start\n", "    self.io_loop.start()\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/tornado/platform/asyncio.py\", line 211, in start\n", "    self.asyncio_loop.run_forever()\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/asyncio/base_events.py\", line 608, in run_forever\n", "    self._run_once()\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/asyncio/base_events.py\", line 1936, in _run_once\n", "    handle._run()\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/asyncio/events.py\", line 84, in _run\n", "    self._context.run(self._callback, *self._args)\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/ipykernel/kernelbase.py\", line 545, in dispatch_queue\n", "    await self.process_one()\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/ipykernel/kernelbase.py\", line 534, in process_one\n", "    await dispatch(*args)\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/ipykernel/kernelbase.py\", line 437, in dispatch_shell\n", "    await result\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/ipykernel/ipkernel.py\", line 362, in execute_request\n", "    await super().execute_request(stream, ident, parent)\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/ipykernel/kernelbase.py\", line 778, in execute_request\n", "    reply_content = await reply_content\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/ipykernel/ipkernel.py\", line 449, in do_execute\n", "    res = shell.run_cell(\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/ipykernel/zmqshell.py\", line 549, in run_cell\n", "    return super().run_cell(*args, **kwargs)\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/IPython/core/interactiveshell.py\", line 3100, in run_cell\n", "    result = self._run_cell(\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/IPython/core/interactiveshell.py\", line 3155, in _run_cell\n", "    result = runner(coro)\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/IPython/core/async_helpers.py\", line 128, in _pseudo_sync_runner\n", "    coro.send(None)\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/IPython/core/interactiveshell.py\", line 3367, in run_cell_async\n", "    has_raised = await self.run_ast_nodes(code_ast.body, cell_name,\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/IPython/core/interactiveshell.py\", line 3612, in run_ast_nodes\n", "    if await self.run_code(code, result, async_=asy):\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/IPython/core/interactiveshell.py\", line 3672, in run_code\n", "    exec(code_obj, self.user_global_ns, self.user_ns)\n", "  File \"/var/folders/gf/nl47jt2n0w9dh5kzsz1rf1mc0000gn/T/ipykernel_78805/4033676583.py\", line 3, in <module>\n", "    logger.info(\"Source bounds:\", source_pcd.get_axis_aligned_bounding_box())\n", "Message: 'Source bounds:'\n", "Arguments: (AxisAlignedBoundingBox: min: (435222, 5.01082e+06, 139.972), max: (436794, 5.01254e+06, 153.136),)\n", "--- Logging error ---\n", "Traceback (most recent call last):\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/logging/__init__.py\", line 1110, in emit\n", "    msg = self.format(record)\n", "          ^^^^^^^^^^^^^^^^^^^\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/logging/__init__.py\", line 953, in format\n", "    return fmt.format(record)\n", "           ^^^^^^^^^^^^^^^^^^\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/logging/__init__.py\", line 687, in format\n", "    record.message = record.getMessage()\n", "                     ^^^^^^^^^^^^^^^^^^^\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/logging/__init__.py\", line 377, in getMessage\n", "    msg = msg % self.args\n", "          ~~~~^~~~~~~~~~~\n", "TypeError: not all arguments converted during string formatting\n", "Call stack:\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/runpy.py\", line 198, in _run_module_as_main\n", "    return _run_code(code, main_globals, None,\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/runpy.py\", line 88, in _run_code\n", "    exec(code, run_globals)\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/ipykernel_launcher.py\", line 18, in <module>\n", "    app.launch_new_instance()\n", "  File \"/Users/<USER>/.local/lib/python3.11/site-packages/traitlets/config/application.py\", line 1075, in launch_instance\n", "    app.start()\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/ipykernel/kernelapp.py\", line 739, in start\n", "    self.io_loop.start()\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/tornado/platform/asyncio.py\", line 211, in start\n", "    self.asyncio_loop.run_forever()\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/asyncio/base_events.py\", line 608, in run_forever\n", "    self._run_once()\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/asyncio/base_events.py\", line 1936, in _run_once\n", "    handle._run()\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/asyncio/events.py\", line 84, in _run\n", "    self._context.run(self._callback, *self._args)\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/ipykernel/kernelbase.py\", line 545, in dispatch_queue\n", "    await self.process_one()\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/ipykernel/kernelbase.py\", line 534, in process_one\n", "    await dispatch(*args)\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/ipykernel/kernelbase.py\", line 437, in dispatch_shell\n", "    await result\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/ipykernel/ipkernel.py\", line 362, in execute_request\n", "    await super().execute_request(stream, ident, parent)\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/ipykernel/kernelbase.py\", line 778, in execute_request\n", "    reply_content = await reply_content\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/ipykernel/ipkernel.py\", line 449, in do_execute\n", "    res = shell.run_cell(\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/ipykernel/zmqshell.py\", line 549, in run_cell\n", "    return super().run_cell(*args, **kwargs)\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/IPython/core/interactiveshell.py\", line 3100, in run_cell\n", "    result = self._run_cell(\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/IPython/core/interactiveshell.py\", line 3155, in _run_cell\n", "    result = runner(coro)\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/IPython/core/async_helpers.py\", line 128, in _pseudo_sync_runner\n", "    coro.send(None)\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/IPython/core/interactiveshell.py\", line 3367, in run_cell_async\n", "    has_raised = await self.run_ast_nodes(code_ast.body, cell_name,\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/IPython/core/interactiveshell.py\", line 3612, in run_ast_nodes\n", "    if await self.run_code(code, result, async_=asy):\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/IPython/core/interactiveshell.py\", line 3672, in run_code\n", "    exec(code_obj, self.user_global_ns, self.user_ns)\n", "  File \"/var/folders/gf/nl47jt2n0w9dh5kzsz1rf1mc0000gn/T/ipykernel_78805/4033676583.py\", line 4, in <module>\n", "    logger.info(\"Target bounds:\", target_pcd.get_axis_aligned_bounding_box())\n", "Message: 'Target bounds:'\n", "Arguments: (AxisAlignedBoundingBox: min: (435267, 5.0109e+06, 152.849), max: (436720, 5.01246e+06, 161.661),)\n", "\n", "Ending Cell 26-----------------------------------------\n", "Executing Cell 27--------------------------------------\n", "Ending Cell 27-----------------------------------------\n", "Executing Cell 28--------------------------------------\n", "INFO:__main__:=== Performance Metrics ===\n", "\n", "INFO:__main__:RMSE: 28.282253\n", "\n", "INFO:__main__:Mean Distance: 18.599182\n", "\n", "INFO:__main__:Median Distance: 5.579951\n", "\n", "INFO:__main__:Std Distance: 21.306250\n", "\n", "INFO:__main__:Max Distance: 141.780196\n", "\n", "INFO:__main__:Min Distance: 0.025563\n", "\n", "INFO:__main__:Accuracy (< 0.01): 0.00%\n", "\n", "INFO:__main__:Accuracy (< 0.05): 0.00%\n", "\n", "INFO:__main__:Accuracy (< 0.1): 0.01%\n", "\n", "INFO:__main__:Saved aligned point cloud to:\n", "\n", "INFO:__main__:  PCD: ../../data/output_runs/alignment_testing/csf/trino_enel/trino_enel/icp_results/icp_aligned_source.pcd\n", "\n", "INFO:__main__:  PLY: ../../data/output_runs/alignment_testing/csf/trino_enel/trino_enel/icp_results/icp_aligned_source.ply\n", "\n", "INFO:__main__:Saved metadata to: ../../data/output_runs/alignment_testing/csf/trino_enel/trino_enel/icp_results/icp_alignment_metadata.json\n", "\n", "Ending Cell 28-----------------------------------------\n", "Executing Cell 29--------------------------------------\n", "Ending Cell 29-----------------------------------------\n", "Executing Cell 30--------------------------------------\n", "INFO:__main__:Logging results to MLflow...\n", "\n", "INFO:__main__:MLflow logging completed successfully.\n", "\n", "INFO:__main__:MLflow run ended.\n", "\n", "INFO:__main__:=== ICP Alignment Completed Successfully ===\n", "\n", "INFO:__main__:Results saved to: ../../data/output_runs/alignment_testing/csf/trino_enel/trino_enel/icp_results\n", "\n", "INFO:__main__:Execution time: 3.4907 seconds\n", "\n", "INFO:__main__:Final RMSE: 28.282253\n", "\n", "Ending Cell 30-----------------------------------------\n", "Executing Cell 31--------------------------------------\n", "INFO:__main__:Results exported:\n", "\n", "INFO:__main__:  Individual: ../../data/output_runs/alignment_testing/csf/results/icp_pmf_results.json\n", "\n", "INFO:__main__:  Master: ../../data/output_runs/alignment_testing/master_results.csv\n", "\n", "INFO:__main__:\n", "=== Test Completed ===\n", "\n", "INFO:__main__:Ground Method: pmf\n", "\n", "INFO:__main__:Alignment Method: icp\n", "\n", "INFO:__main__:Success: False\n", "\n", "INFO:__main__:RMSE: 28.282253492186637\n", "\n", "INFO:__main__:Time: 3.490652084350586s\n", "\n", "INFO:__main__:Iterations: 50\n", "\n", "Ending Cell 31-----------------------------------------\n", "Executing Cell 32--------------------------------------\n", "Ending Cell 32-----------------------------------------\n", "Executing Cell 33--------------------------------------\n", "Ending Cell 33-----------------------------------------\n"]}], "source": ["# PMF + ICP Alignment Test\n", "!papermill 01_icp_alignment.ipynb \\\n", "    ../../data/output_runs/alignment_testing/pmf/01_icp_alignment_pmf.ipynb \\\n", "    -p ground_method \"pmf\" \\\n", "    -p site_name \"trino_enel\" \\\n", "    -p project_type \"trino_enel\" \\\n", "    --log-output \\\n", "    --kernel pytorch-geo-dev"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### PMF + Neural Network Alignment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# PMF + Neural Network Alignment Test\n", "!papermill 02_neural_network_alignment.ipynb \\\n", "    ../../data/output_runs/alignment_testing/pmf/02_neural_network_alignment_pmf.ipynb \\\n", "    -p ground_method \"pmf\" \\\n", "    -p site_name \"trino_enel\" \\\n", "    -p project_type \"trino_enel\" \\\n", "    --log-output \\\n", "    --kernel pytorch-geo-dev"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### PMF + Hybrid Alignment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# PMF + Hybrid Alignment Test\n", "!papermill 03_hybrid_alignment.ipynb \\\n", "    ../../data/output_runs/alignment_testing/pmf/03_hybrid_alignment_pmf.ipynb \\\n", "    -p ground_method \"pmf\" \\\n", "    -p site_name \"trino_enel\" \\\n", "    -p project_type \"trino_enel\" \\\n", "    --log-output \\\n", "    --kernel pytorch-geo-dev\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## RANSAC Ground Segmentation Tests"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### RANSAC + ICP Alignment"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Input Notebook:  01_icp_alignment.ipynb\n", "Output Notebook: ../../data/output_runs/alignment_testing/ransac/01_icp_alignment_ransac.ipynb\n", "Executing notebook with kernel: pytorch-geo-dev\n", "Executing Cell 1---------------------------------------\n", "Ending Cell 1------------------------------------------\n", "Executing Cell 2---------------------------------------\n", "INFO:__main__:Configuration:\n", "\n", "INFO:__main__:  Ground Method: csf\n", "\n", "INFO:__main__:  Source File: ../../data/processed/trino_enel/ground_segmentation/csf/trino_enel_nonground.ply\n", "\n", "INFO:__main__:  Target File: GRE.EEC.S.00.IT.P.14353.00.265_pointcloud.ply\n", "\n", "INFO:__main__:  Output Dir: ../../data/output_runs/alignment_testing/csf\n", "\n", "INFO:__main__:  MLflow Run: icp_csf_trino_enel\n", "\n", "INFO:__main__:ICP Configuration:\n", "\n", "INFO:__main__:  Max Iterations: 50\n", "\n", "INFO:__main__:  Tolerance: 1e-06\n", "\n", "INFO:__main__:  Distance Threshold: 0.1\n", "\n", "Ending Cell 2------------------------------------------\n", "Executing Cell 3---------------------------------------\n", "Ending Cell 3------------------------------------------\n", "Executing Cell 4---------------------------------------\n", "Ending Cell 4------------------------------------------\n", "Executing Cell 5---------------------------------------\n", "INFO:__main__:File Validation for ransac:\n", "\n", "INFO:__main__:  Source: ../../data/processed/trino_enel/ground_segmentation/csf/trino_enel_nonground.ply\n", "\n", "INFO:__main__:  Target: ../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_pointcloud.ply\n", "\n", "INFO:__main__:File validation passed - ready to proceed\n", "\n", "INFO:__main__:Final source file: ../../data/processed/trino_enel/ground_segmentation/csf/trino_enel_nonground.ply\n", "\n", "INFO:__main__:Final target file: ../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_pointcloud.ply\n", "\n", "Ending Cell 5------------------------------------------\n", "Executing Cell 6---------------------------------------\n", "Ending Cell 6------------------------------------------\n", "Executing Cell 7---------------------------------------\n", "Ending Cell 7------------------------------------------\n", "Executing Cell 8---------------------------------------\n", "Ending Cell 8------------------------------------------\n", "Executing Cell 9---------------------------------------\n", "No handler found for comm target 'dash'\n", "INFO:__main__:ICP Alignment Environment Initialized\n", "\n", "INFO:__main__:Open3D version: 0.19.0\n", "\n", "INFO:__main__:Analysis Date: 2025-07-04 21:32:55\n", "\n", "Ending Cell 9------------------------------------------\n", "Executing Cell 10--------------------------------------\n", "Ending Cell 10-----------------------------------------\n", "Executing Cell 11--------------------------------------\n", "INFO:__main__:Project: trino_enel/trino_enel\n", "\n", "INFO:__main__:Input path: ../../data/trino_enel/trino_enel/preprocessing\n", "\n", "INFO:__main__:Output path: ../../data/output_runs/alignment_testing/csf/trino_enel/trino_enel/icp_results\n", "\n", "INFO:__main__:Source file: ../../data/processed/trino_enel/ground_segmentation/csf/trino_enel_nonground.ply\n", "\n", "INFO:__main__:Target file: ../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_pointcloud.ply\n", "\n", "INFO:__main__:MLflow experiment initialized\n", "\n", "Ending Cell 11-----------------------------------------\n", "Executing Cell 12--------------------------------------\n", "Ending Cell 12-----------------------------------------\n", "Executing Cell 13--------------------------------------\n", "Ending Cell 13-----------------------------------------\n", "Executing Cell 14--------------------------------------\n", "Ending Cell 14-----------------------------------------\n", "Executing Cell 15--------------------------------------\n", "Ending Cell 15-----------------------------------------\n", "Executing Cell 16--------------------------------------\n", "Ending Cell 16-----------------------------------------\n", "Executing Cell 17--------------------------------------\n", "Ending Cell 17-----------------------------------------\n", "Executing Cell 18--------------------------------------\n", "Ending Cell 18-----------------------------------------\n", "Executing Cell 19--------------------------------------\n", "Ending Cell 19-----------------------------------------\n", "Executing Cell 20--------------------------------------\n", "Ending Cell 20-----------------------------------------\n", "Executing Cell 21--------------------------------------\n", "Ending Cell 21-----------------------------------------\n", "Executing Cell 22--------------------------------------\n", "Ending Cell 22-----------------------------------------\n", "Executing Cell 23--------------------------------------\n", "total 9776\n", "drwxr-xr-x@ 3 <USER>  <GROUP>    96B Jul  4 20:14 \u001b[34mGRE.EEC.S.00.IT.P.14353.00.265\u001b[m\u001b[m\n", "-rw-r--r--@ 1 <USER>  <GROUP>   4.8M Jul  4 20:15 GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\n", "-rw-r--r--@ 1 <USER>  <GROUP>   1.1K Jul  4 20:15 GRE.EEC.S.00.IT.P.14353.00.265_data_driven.report.txt\n", "-rw-r--r--@ 1 <USER>  <GROUP>   923B Jul  4 20:15 GRE.EEC.S.00.IT.P.14353.00.265_data_driven.strategy.json\n", "drwxr-xr-x@ 2 <USER>  <GROUP>    64B Jul  2 13:18 \u001b[34mGRE.EEC.S.00.IT.P.14353.00.265_pointcloud.ply\u001b[m\u001b[m\n", "\n", "Ending Cell 23-----------------------------------------\n", "Executing Cell 24--------------------------------------\n", "INFO:__main__:Loading point cloud data...\n", "\n", "INFO:__main__:Loading source: ../../data/processed/trino_enel/ground_segmentation/ransac/trino_enel_nonground.ply\n", "\n", "INFO:__main__:Reading point cloud file: ../../data/processed/trino_enel/ground_segmentation/ransac/trino_enel_nonground.ply\n", "\n", "INFO:__main__:Loaded 22724 points from ../../data/processed/trino_enel/ground_segmentation/ransac/trino_enel_nonground.ply\n", "\n", "INFO:__main__:Loading target: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\n", "\n", "INFO:__main__:Reading point cloud file: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\n", "\n", "INFO:__main__:Loaded 118920 points from /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\n", "\n", "INFO:__main__:Loaded source shape: (22724, 3), target shape: (118920, 3)\n", "\n", "INFO:__main__:\n", "Preprocessing point clouds...\n", "\n", "INFO:__main__:Downsampling with voxel size: 0.02\n", "\n", "INFO:__main__:Downsampled source: 22724 points\n", "\n", "INFO:__main__:Downsampled target: 118916 points\n", "\n", "INFO:__main__:=== Executing ICP Alignment ===\n", "\n", "INFO:__main__:Iteration   1, MSE: 24677.6492365444\n", "\n", "INFO:__main__:Iteration   2, MSE: 1126.0184387664\n", "\n", "INFO:__main__:Iteration   3, MSE: 1108.7440884581\n", "\n", "INFO:__main__:Iteration   4, MSE: 1097.0926182834\n", "\n", "INFO:__main__:Iteration   5, MSE: 1089.2941539665\n", "\n", "INFO:__main__:Iteration   6, MSE: 1085.0215707322\n", "\n", "INFO:__main__:Iteration   7, MSE: 1082.6429398258\n", "\n", "INFO:__main__:Iteration   8, MSE: 1081.1079995342\n", "\n", "INFO:__main__:Iteration   9, MSE: 1080.0184832616\n", "\n", "INFO:__main__:Iteration  10, MSE: 1079.1839597321\n", "\n", "INFO:__main__:Iteration  11, MSE: 1078.6048096844\n", "\n", "INFO:__main__:Iteration  12, MSE: 1078.2576568314\n", "\n", "INFO:__main__:Iteration  13, MSE: 1078.0530004933\n", "\n", "INFO:__main__:Iteration  14, MSE: 1077.9341139037\n", "\n", "INFO:__main__:Iteration  15, MSE: 1077.8641899665\n", "\n", "INFO:__main__:Iteration  16, MSE: 1077.8231151019\n", "\n", "INFO:__main__:Iteration  17, MSE: 1077.7992108774\n", "\n", "INFO:__main__:Iteration  18, MSE: 1077.7857255149\n", "\n", "INFO:__main__:Iteration  19, MSE: 1077.7801570605\n", "\n", "INFO:__main__:Iteration  20, MSE: 1077.7767049795\n", "\n", "INFO:__main__:Iteration  21, MSE: 1077.7746151271\n", "\n", "INFO:__main__:Iteration  22, MSE: 1077.7733526821\n", "\n", "INFO:__main__:Iteration  23, MSE: 1077.7726985172\n", "\n", "INFO:__main__:Iteration  24, MSE: 1077.7722334042\n", "\n", "INFO:__main__:Iteration  25, MSE: 1077.7719127947\n", "\n", "INFO:__main__:Iteration  26, MSE: 1077.7717642223\n", "\n", "INFO:__main__:Iteration  27, MSE: 1077.7716846218\n", "\n", "INFO:__main__:Iteration  28, MSE: 1077.7716469829\n", "\n", "INFO:__main__:Iteration  29, MSE: 1077.7716300647\n", "\n", "INFO:__main__:Iteration  30, MSE: 1077.7716051405\n", "\n", "INFO:__main__:Iteration  31, MSE: 1077.7715766754\n", "\n", "INFO:__main__:Iteration  32, MSE: 1077.7715485796\n", "\n", "INFO:__main__:Iteration  33, MSE: 1077.7715130592\n", "\n", "INFO:__main__:Iteration  34, MSE: 1077.7715011551\n", "\n", "INFO:__main__:Iteration  35, MSE: 1077.7714867616\n", "\n", "INFO:__main__:Iteration  36, MSE: 1077.7714731629\n", "\n", "INFO:__main__:Iteration  37, MSE: 1077.7714654063\n", "\n", "INFO:__main__:Iteration  38, MSE: 1077.7714615514\n", "\n", "INFO:__main__:Iteration  39, MSE: 1077.7714593149\n", "\n", "INFO:__main__:Iteration  40, MSE: 1077.7714554897\n", "\n", "INFO:__main__:Iteration  41, MSE: 1077.7714544146\n", "\n", "INFO:__main__:Iteration  42, MSE: 1077.7714543265\n", "\n", "INFO:__main__:Converged after 42 iterations.\n", "\n", "INFO:__main__:ICP completed in 2.3305 seconds\n", "\n", "INFO:__main__:=== ICP Results ===\n", "\n", "INFO:__main__:Execution time: 2.3311 seconds\n", "\n", "INFO:__main__:Iterations: 42\n", "\n", "INFO:__main__:Final MSE: 1077.7714543265\n", "\n", "INFO:__main__:Converged: True\n", "\n", "Ending Cell 24-----------------------------------------\n", "Executing Cell 25--------------------------------------\n", "INFO:__main__:Loading source: ../../data/processed/trino_enel/ground_segmentation/ransac/trino_enel_nonground.ply\n", "\n", "INFO:__main__:Loading target: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\n", "\n", "Ending Cell 25-----------------------------------------\n", "Executing Cell 26--------------------------------------\n", "INFO:__main__:Source has points: True, count: 22724\n", "\n", "INFO:__main__:Target has points: True, count: 118920\n", "\n", "--- Logging error ---\n", "Traceback (most recent call last):\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/logging/__init__.py\", line 1110, in emit\n", "    msg = self.format(record)\n", "          ^^^^^^^^^^^^^^^^^^^\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/logging/__init__.py\", line 953, in format\n", "    return fmt.format(record)\n", "           ^^^^^^^^^^^^^^^^^^\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/logging/__init__.py\", line 687, in format\n", "    record.message = record.getMessage()\n", "                     ^^^^^^^^^^^^^^^^^^^\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/logging/__init__.py\", line 377, in getMessage\n", "    msg = msg % self.args\n", "          ~~~~^~~~~~~~~~~\n", "TypeError: not all arguments converted during string formatting\n", "Call stack:\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/runpy.py\", line 198, in _run_module_as_main\n", "    return _run_code(code, main_globals, None,\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/runpy.py\", line 88, in _run_code\n", "    exec(code, run_globals)\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/ipykernel_launcher.py\", line 18, in <module>\n", "    app.launch_new_instance()\n", "  File \"/Users/<USER>/.local/lib/python3.11/site-packages/traitlets/config/application.py\", line 1075, in launch_instance\n", "    app.start()\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/ipykernel/kernelapp.py\", line 739, in start\n", "    self.io_loop.start()\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/tornado/platform/asyncio.py\", line 211, in start\n", "    self.asyncio_loop.run_forever()\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/asyncio/base_events.py\", line 608, in run_forever\n", "    self._run_once()\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/asyncio/base_events.py\", line 1936, in _run_once\n", "    handle._run()\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/asyncio/events.py\", line 84, in _run\n", "    self._context.run(self._callback, *self._args)\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/ipykernel/kernelbase.py\", line 545, in dispatch_queue\n", "    await self.process_one()\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/ipykernel/kernelbase.py\", line 534, in process_one\n", "    await dispatch(*args)\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/ipykernel/kernelbase.py\", line 437, in dispatch_shell\n", "    await result\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/ipykernel/ipkernel.py\", line 362, in execute_request\n", "    await super().execute_request(stream, ident, parent)\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/ipykernel/kernelbase.py\", line 778, in execute_request\n", "    reply_content = await reply_content\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/ipykernel/ipkernel.py\", line 449, in do_execute\n", "    res = shell.run_cell(\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/ipykernel/zmqshell.py\", line 549, in run_cell\n", "    return super().run_cell(*args, **kwargs)\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/IPython/core/interactiveshell.py\", line 3100, in run_cell\n", "    result = self._run_cell(\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/IPython/core/interactiveshell.py\", line 3155, in _run_cell\n", "    result = runner(coro)\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/IPython/core/async_helpers.py\", line 128, in _pseudo_sync_runner\n", "    coro.send(None)\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/IPython/core/interactiveshell.py\", line 3367, in run_cell_async\n", "    has_raised = await self.run_ast_nodes(code_ast.body, cell_name,\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/IPython/core/interactiveshell.py\", line 3612, in run_ast_nodes\n", "    if await self.run_code(code, result, async_=asy):\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/IPython/core/interactiveshell.py\", line 3672, in run_code\n", "    exec(code_obj, self.user_global_ns, self.user_ns)\n", "  File \"/var/folders/gf/nl47jt2n0w9dh5kzsz1rf1mc0000gn/T/ipykernel_78869/4033676583.py\", line 3, in <module>\n", "    logger.info(\"Source bounds:\", source_pcd.get_axis_aligned_bounding_box())\n", "Message: 'Source bounds:'\n", "Arguments: (AxisAlignedBoundingBox: min: (435222, 5.01082e+06, 139.9), max: (436794, 5.01254e+06, 153.136),)\n", "--- Logging error ---\n", "Traceback (most recent call last):\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/logging/__init__.py\", line 1110, in emit\n", "    msg = self.format(record)\n", "          ^^^^^^^^^^^^^^^^^^^\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/logging/__init__.py\", line 953, in format\n", "    return fmt.format(record)\n", "           ^^^^^^^^^^^^^^^^^^\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/logging/__init__.py\", line 687, in format\n", "    record.message = record.getMessage()\n", "                     ^^^^^^^^^^^^^^^^^^^\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/logging/__init__.py\", line 377, in getMessage\n", "    msg = msg % self.args\n", "          ~~~~^~~~~~~~~~~\n", "TypeError: not all arguments converted during string formatting\n", "Call stack:\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/runpy.py\", line 198, in _run_module_as_main\n", "    return _run_code(code, main_globals, None,\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/runpy.py\", line 88, in _run_code\n", "    exec(code, run_globals)\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/ipykernel_launcher.py\", line 18, in <module>\n", "    app.launch_new_instance()\n", "  File \"/Users/<USER>/.local/lib/python3.11/site-packages/traitlets/config/application.py\", line 1075, in launch_instance\n", "    app.start()\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/ipykernel/kernelapp.py\", line 739, in start\n", "    self.io_loop.start()\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/tornado/platform/asyncio.py\", line 211, in start\n", "    self.asyncio_loop.run_forever()\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/asyncio/base_events.py\", line 608, in run_forever\n", "    self._run_once()\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/asyncio/base_events.py\", line 1936, in _run_once\n", "    handle._run()\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/asyncio/events.py\", line 84, in _run\n", "    self._context.run(self._callback, *self._args)\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/ipykernel/kernelbase.py\", line 545, in dispatch_queue\n", "    await self.process_one()\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/ipykernel/kernelbase.py\", line 534, in process_one\n", "    await dispatch(*args)\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/ipykernel/kernelbase.py\", line 437, in dispatch_shell\n", "    await result\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/ipykernel/ipkernel.py\", line 362, in execute_request\n", "    await super().execute_request(stream, ident, parent)\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/ipykernel/kernelbase.py\", line 778, in execute_request\n", "    reply_content = await reply_content\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/ipykernel/ipkernel.py\", line 449, in do_execute\n", "    res = shell.run_cell(\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/ipykernel/zmqshell.py\", line 549, in run_cell\n", "    return super().run_cell(*args, **kwargs)\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/IPython/core/interactiveshell.py\", line 3100, in run_cell\n", "    result = self._run_cell(\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/IPython/core/interactiveshell.py\", line 3155, in _run_cell\n", "    result = runner(coro)\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/IPython/core/async_helpers.py\", line 128, in _pseudo_sync_runner\n", "    coro.send(None)\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/IPython/core/interactiveshell.py\", line 3367, in run_cell_async\n", "    has_raised = await self.run_ast_nodes(code_ast.body, cell_name,\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/IPython/core/interactiveshell.py\", line 3612, in run_ast_nodes\n", "    if await self.run_code(code, result, async_=asy):\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/IPython/core/interactiveshell.py\", line 3672, in run_code\n", "    exec(code_obj, self.user_global_ns, self.user_ns)\n", "  File \"/var/folders/gf/nl47jt2n0w9dh5kzsz1rf1mc0000gn/T/ipykernel_78869/4033676583.py\", line 4, in <module>\n", "    logger.info(\"Target bounds:\", target_pcd.get_axis_aligned_bounding_box())\n", "Message: 'Target bounds:'\n", "Arguments: (AxisAlignedBoundingBox: min: (435267, 5.0109e+06, 152.849), max: (436720, 5.01246e+06, 161.661),)\n", "\n", "Ending Cell 26-----------------------------------------\n", "Executing Cell 27--------------------------------------\n", "Ending Cell 27-----------------------------------------\n", "Executing Cell 28--------------------------------------\n", "INFO:__main__:=== Performance Metrics ===\n", "\n", "INFO:__main__:RMSE: 32.829430\n", "\n", "INFO:__main__:Mean Distance: 23.724389\n", "\n", "INFO:__main__:Median Distance: 14.945265\n", "\n", "INFO:__main__:Std Distance: 22.691955\n", "\n", "INFO:__main__:Max Distance: 132.737424\n", "\n", "INFO:__main__:Min Distance: 0.140269\n", "\n", "INFO:__main__:Accuracy (< 0.01): 0.00%\n", "\n", "INFO:__main__:Accuracy (< 0.05): 0.00%\n", "\n", "INFO:__main__:Accuracy (< 0.1): 0.00%\n", "\n", "INFO:__main__:Saved aligned point cloud to:\n", "\n", "INFO:__main__:  PCD: ../../data/output_runs/alignment_testing/csf/trino_enel/trino_enel/icp_results/icp_aligned_source.pcd\n", "\n", "INFO:__main__:  PLY: ../../data/output_runs/alignment_testing/csf/trino_enel/trino_enel/icp_results/icp_aligned_source.ply\n", "\n", "INFO:__main__:Saved metadata to: ../../data/output_runs/alignment_testing/csf/trino_enel/trino_enel/icp_results/icp_alignment_metadata.json\n", "\n", "Ending Cell 28-----------------------------------------\n", "Executing Cell 29--------------------------------------\n", "Ending Cell 29-----------------------------------------\n", "Executing Cell 30--------------------------------------\n", "INFO:__main__:Logging results to MLflow...\n", "\n", "INFO:__main__:MLflow logging completed successfully.\n", "\n", "INFO:__main__:MLflow run ended.\n", "\n", "INFO:__main__:=== ICP Alignment Completed Successfully ===\n", "\n", "INFO:__main__:Results saved to: ../../data/output_runs/alignment_testing/csf/trino_enel/trino_enel/icp_results\n", "\n", "INFO:__main__:Execution time: 2.3311 seconds\n", "\n", "INFO:__main__:Final RMSE: 32.829430\n", "\n", "Ending Cell 30-----------------------------------------\n", "Executing Cell 31--------------------------------------\n", "INFO:__main__:Results exported:\n", "\n", "INFO:__main__:  Individual: ../../data/output_runs/alignment_testing/csf/results/icp_ransac_results.json\n", "\n", "INFO:__main__:  Master: ../../data/output_runs/alignment_testing/master_results.csv\n", "\n", "INFO:__main__:\n", "=== Test Completed ===\n", "\n", "INFO:__main__:Ground Method: ransac\n", "\n", "INFO:__main__:Alignment Method: icp\n", "\n", "INFO:__main__:Success: True\n", "\n", "INFO:__main__:RMSE: 32.829429698465134\n", "\n", "INFO:__main__:Time: 2.331051826477051s\n", "\n", "INFO:__main__:Iterations: 42\n", "\n", "Ending Cell 31-----------------------------------------\n", "Executing Cell 32--------------------------------------\n", "Ending Cell 32-----------------------------------------\n", "Executing Cell 33--------------------------------------\n", "Ending Cell 33-----------------------------------------\n"]}], "source": ["# RANSAC + ICP Alignment Test\n", "!papermill 01_icp_alignment.ipynb \\\n", "    ../../data/output_runs/alignment_testing/ransac/01_icp_alignment_ransac.ipynb \\\n", "    -p ground_method \"ransac\" \\\n", "    -p site_name \"trino_enel\" \\\n", "    -p project_type \"trino_enel\" \\\n", "    --log-output \\\n", "    --kernel pytorch-geo-dev\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### RANSAC + Neural Network Alignment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# RANSAC + Neural Network Alignment Test\n", "!papermill 02_neural_network_alignment.ipynb \\\n", "    ../../data/output_runs/alignment_testing/ransac/02_neural_network_alignment_ransac.ipynb \\\n", "    -p ground_method \"ransac\" \\\n", "    -p site_name \"trino_enel\" \\\n", "    -p project_type \"trino_enel\" \\\n", "    --log-output \\\n", "    --kernel pytorch-geo-dev\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### RANSAC + Hybrid Alignment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# RANSAC + Hybrid Alignment Test\n", "!papermill 03_hybrid_alignment.ipynb \\\n", "    ../../data/output_runs/alignment_testing/ransac/03_hybrid_alignment_ransac.ipynb \\\n", "    -p ground_method \"ransac\" \\\n", "    -p site_name \"trino_enel\" \\\n", "    -p project_type \"trino_enel\" \\\n", "    --log-output \\\n", "    --kernel pytorch-geo-dev\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## RANSAC+PMF Ground Segmentation Tests"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### RANSAC+PMF + ICP Alignment"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Input Notebook:  01_icp_alignment.ipynb\n", "Output Notebook: ../../data/output_runs/alignment_testing/ransac_pmf/01_icp_alignment_ransac_pmf.ipynb\n", "Executing notebook with kernel: pytorch-geo-dev\n", "Executing Cell 1---------------------------------------\n", "Ending Cell 1------------------------------------------\n", "Executing Cell 2---------------------------------------\n", "INFO:__main__:Configuration:\n", "\n", "INFO:__main__:  Ground Method: csf\n", "\n", "INFO:__main__:  Source File: ../../data/processed/trino_enel/ground_segmentation/csf/trino_enel_nonground.ply\n", "\n", "INFO:__main__:  Target File: GRE.EEC.S.00.IT.P.14353.00.265_pointcloud.ply\n", "\n", "INFO:__main__:  Output Dir: ../../data/output_runs/alignment_testing/csf\n", "\n", "INFO:__main__:  MLflow Run: icp_csf_trino_enel\n", "\n", "INFO:__main__:ICP Configuration:\n", "\n", "INFO:__main__:  Max Iterations: 50\n", "\n", "INFO:__main__:  Tolerance: 1e-06\n", "\n", "INFO:__main__:  Distance Threshold: 0.1\n", "\n", "Ending Cell 2------------------------------------------\n", "Executing Cell 3---------------------------------------\n", "Ending Cell 3------------------------------------------\n", "Executing Cell 4---------------------------------------\n", "Ending Cell 4------------------------------------------\n", "Executing Cell 5---------------------------------------\n", "INFO:__main__:File Validation for ransac_pmf:\n", "\n", "INFO:__main__:  Source: ../../data/processed/trino_enel/ground_segmentation/csf/trino_enel_nonground.ply\n", "\n", "INFO:__main__:  Target: ../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_pointcloud.ply\n", "\n", "INFO:__main__:File validation passed - ready to proceed\n", "\n", "INFO:__main__:Final source file: ../../data/processed/trino_enel/ground_segmentation/csf/trino_enel_nonground.ply\n", "\n", "INFO:__main__:Final target file: ../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_pointcloud.ply\n", "\n", "Ending Cell 5------------------------------------------\n", "Executing Cell 6---------------------------------------\n", "Ending Cell 6------------------------------------------\n", "Executing Cell 7---------------------------------------\n", "Ending Cell 7------------------------------------------\n", "Executing Cell 8---------------------------------------\n", "Ending Cell 8------------------------------------------\n", "Executing Cell 9---------------------------------------\n", "No handler found for comm target 'dash'\n", "INFO:__main__:ICP Alignment Environment Initialized\n", "\n", "INFO:__main__:Open3D version: 0.19.0\n", "\n", "INFO:__main__:Analysis Date: 2025-07-04 21:33:07\n", "\n", "Ending Cell 9------------------------------------------\n", "Executing Cell 10--------------------------------------\n", "Ending Cell 10-----------------------------------------\n", "Executing Cell 11--------------------------------------\n", "INFO:__main__:Project: trino_enel/trino_enel\n", "\n", "INFO:__main__:Input path: ../../data/trino_enel/trino_enel/preprocessing\n", "\n", "INFO:__main__:Output path: ../../data/output_runs/alignment_testing/csf/trino_enel/trino_enel/icp_results\n", "\n", "INFO:__main__:Source file: ../../data/processed/trino_enel/ground_segmentation/csf/trino_enel_nonground.ply\n", "\n", "INFO:__main__:Target file: ../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_pointcloud.ply\n", "\n", "INFO:__main__:MLflow experiment initialized\n", "\n", "Ending Cell 11-----------------------------------------\n", "Executing Cell 12--------------------------------------\n", "Ending Cell 12-----------------------------------------\n", "Executing Cell 13--------------------------------------\n", "Ending Cell 13-----------------------------------------\n", "Executing Cell 14--------------------------------------\n", "Ending Cell 14-----------------------------------------\n", "Executing Cell 15--------------------------------------\n", "Ending Cell 15-----------------------------------------\n", "Executing Cell 16--------------------------------------\n", "Ending Cell 16-----------------------------------------\n", "Executing Cell 17--------------------------------------\n", "Ending Cell 17-----------------------------------------\n", "Executing Cell 18--------------------------------------\n", "Ending Cell 18-----------------------------------------\n", "Executing Cell 19--------------------------------------\n", "Ending Cell 19-----------------------------------------\n", "Executing Cell 20--------------------------------------\n", "Ending Cell 20-----------------------------------------\n", "Executing Cell 21--------------------------------------\n", "Ending Cell 21-----------------------------------------\n", "Executing Cell 22--------------------------------------\n", "Ending Cell 22-----------------------------------------\n", "Executing Cell 23--------------------------------------\n", "total 9776\n", "drwxr-xr-x@ 3 <USER>  <GROUP>    96B Jul  4 20:14 \u001b[34mGRE.EEC.S.00.IT.P.14353.00.265\u001b[m\u001b[m\n", "-rw-r--r--@ 1 <USER>  <GROUP>   4.8M Jul  4 20:15 GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\n", "-rw-r--r--@ 1 <USER>  <GROUP>   1.1K Jul  4 20:15 GRE.EEC.S.00.IT.P.14353.00.265_data_driven.report.txt\n", "-rw-r--r--@ 1 <USER>  <GROUP>   923B Jul  4 20:15 GRE.EEC.S.00.IT.P.14353.00.265_data_driven.strategy.json\n", "drwxr-xr-x@ 2 <USER>  <GROUP>    64B Jul  2 13:18 \u001b[34mGRE.EEC.S.00.IT.P.14353.00.265_pointcloud.ply\u001b[m\u001b[m\n", "\n", "Ending Cell 23-----------------------------------------\n", "Executing Cell 24--------------------------------------\n", "INFO:__main__:Loading point cloud data...\n", "\n", "INFO:__main__:Loading source: ../../data/processed/trino_enel/ground_segmentation/ransac_pmf/trino_enel_nonground.ply\n", "\n", "INFO:__main__:Reading point cloud file: ../../data/processed/trino_enel/ground_segmentation/ransac_pmf/trino_enel_nonground.ply\n", "\n", "INFO:__main__:Loaded 22689 points from ../../data/processed/trino_enel/ground_segmentation/ransac_pmf/trino_enel_nonground.ply\n", "\n", "INFO:__main__:Loading target: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\n", "\n", "INFO:__main__:Reading point cloud file: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\n", "\n", "INFO:__main__:Loaded 118920 points from /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\n", "\n", "INFO:__main__:Loaded source shape: (22689, 3), target shape: (118920, 3)\n", "\n", "INFO:__main__:\n", "Preprocessing point clouds...\n", "\n", "INFO:__main__:Downsampling with voxel size: 0.02\n", "\n", "INFO:__main__:Downsampled source: 22689 points\n", "\n", "INFO:__main__:Downsampled target: 118916 points\n", "\n", "INFO:__main__:=== Executing ICP Alignment ===\n", "\n", "INFO:__main__:Iteration   1, MSE: 24677.7465403076\n", "\n", "INFO:__main__:Iteration   2, MSE: 1126.7444199305\n", "\n", "INFO:__main__:Iteration   3, MSE: 1109.5326467644\n", "\n", "INFO:__main__:Iteration   4, MSE: 1097.9513987074\n", "\n", "INFO:__main__:Iteration   5, MSE: 1090.1659745464\n", "\n", "INFO:__main__:Iteration   6, MSE: 1085.9068115852\n", "\n", "INFO:__main__:Iteration   7, MSE: 1083.5522212333\n", "\n", "INFO:__main__:Iteration   8, MSE: 1082.0473303953\n", "\n", "INFO:__main__:Iteration   9, MSE: 1080.9517645484\n", "\n", "INFO:__main__:Iteration  10, MSE: 1080.1238276469\n", "\n", "INFO:__main__:Iteration  11, MSE: 1079.5370222698\n", "\n", "INFO:__main__:Iteration  12, MSE: 1079.1866848769\n", "\n", "INFO:__main__:Iteration  13, MSE: 1078.9889367840\n", "\n", "INFO:__main__:Iteration  14, MSE: 1078.8669889600\n", "\n", "INFO:__main__:Iteration  15, MSE: 1078.7981310824\n", "\n", "INFO:__main__:Iteration  16, MSE: 1078.7545548518\n", "\n", "INFO:__main__:Iteration  17, MSE: 1078.7291686629\n", "\n", "INFO:__main__:Iteration  18, MSE: 1078.7156395942\n", "\n", "INFO:__main__:Iteration  19, MSE: 1078.7090430241\n", "\n", "INFO:__main__:Iteration  20, MSE: 1078.7051369759\n", "\n", "INFO:__main__:Iteration  21, MSE: 1078.7031649152\n", "\n", "INFO:__main__:Iteration  22, MSE: 1078.7019757089\n", "\n", "INFO:__main__:Iteration  23, MSE: 1078.7013813852\n", "\n", "INFO:__main__:Iteration  24, MSE: 1078.7010072089\n", "\n", "INFO:__main__:Iteration  25, MSE: 1078.7006386380\n", "\n", "INFO:__main__:Iteration  26, MSE: 1078.7005061591\n", "\n", "INFO:__main__:Iteration  27, MSE: 1078.7004520093\n", "\n", "INFO:__main__:Iteration  28, MSE: 1078.7004201136\n", "\n", "INFO:__main__:Iteration  29, MSE: 1078.7004022166\n", "\n", "INFO:__main__:Iteration  30, MSE: 1078.7003945407\n", "\n", "INFO:__main__:Iteration  31, MSE: 1078.7003868870\n", "\n", "INFO:__main__:Iteration  32, MSE: 1078.7003804088\n", "\n", "INFO:__main__:Iteration  33, MSE: 1078.7003782935\n", "\n", "INFO:__main__:Iteration  34, MSE: 1078.7003758207\n", "\n", "INFO:__main__:Iteration  35, MSE: 1078.7003715926\n", "\n", "INFO:__main__:Iteration  36, MSE: 1078.7003625872\n", "\n", "INFO:__main__:Iteration  37, MSE: 1078.7003554881\n", "\n", "INFO:__main__:Iteration  38, MSE: 1078.7003481084\n", "\n", "INFO:__main__:Iteration  39, MSE: 1078.7003434008\n", "\n", "INFO:__main__:Iteration  40, MSE: 1078.7003337296\n", "\n", "INFO:__main__:Iteration  41, MSE: 1078.7003215146\n", "\n", "INFO:__main__:Iteration  42, MSE: 1078.7003170025\n", "\n", "INFO:__main__:Iteration  43, MSE: 1078.7003130467\n", "\n", "INFO:__main__:Iteration  44, MSE: 1078.7003085327\n", "\n", "INFO:__main__:Iteration  45, MSE: 1078.7003064797\n", "\n", "INFO:__main__:Iteration  46, MSE: 1078.7003036618\n", "\n", "INFO:__main__:Iteration  47, MSE: 1078.7002998514\n", "\n", "INFO:__main__:Iteration  48, MSE: 1078.7002976481\n", "\n", "INFO:__main__:Iteration  49, MSE: 1078.7002973374\n", "\n", "INFO:__main__:Converged after 49 iterations.\n", "\n", "INFO:__main__:ICP completed in 2.7668 seconds\n", "\n", "INFO:__main__:=== ICP Results ===\n", "\n", "INFO:__main__:Execution time: 2.7674 seconds\n", "\n", "INFO:__main__:Iterations: 49\n", "\n", "INFO:__main__:Final MSE: 1078.7002973374\n", "\n", "INFO:__main__:Converged: True\n", "\n", "Ending Cell 24-----------------------------------------\n", "Executing Cell 25--------------------------------------\n", "INFO:__main__:Loading source: ../../data/processed/trino_enel/ground_segmentation/ransac_pmf/trino_enel_nonground.ply\n", "\n", "INFO:__main__:Loading target: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\n", "\n", "Ending Cell 25-----------------------------------------\n", "Executing Cell 26--------------------------------------\n", "INFO:__main__:Source has points: True, count: 22689\n", "\n", "INFO:__main__:Target has points: True, count: 118920\n", "\n", "--- Logging error ---\n", "Traceback (most recent call last):\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/logging/__init__.py\", line 1110, in emit\n", "    msg = self.format(record)\n", "          ^^^^^^^^^^^^^^^^^^^\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/logging/__init__.py\", line 953, in format\n", "    return fmt.format(record)\n", "           ^^^^^^^^^^^^^^^^^^\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/logging/__init__.py\", line 687, in format\n", "    record.message = record.getMessage()\n", "                     ^^^^^^^^^^^^^^^^^^^\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/logging/__init__.py\", line 377, in getMessage\n", "    msg = msg % self.args\n", "          ~~~~^~~~~~~~~~~\n", "TypeError: not all arguments converted during string formatting\n", "Call stack:\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/runpy.py\", line 198, in _run_module_as_main\n", "    return _run_code(code, main_globals, None,\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/runpy.py\", line 88, in _run_code\n", "    exec(code, run_globals)\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/ipykernel_launcher.py\", line 18, in <module>\n", "    app.launch_new_instance()\n", "  File \"/Users/<USER>/.local/lib/python3.11/site-packages/traitlets/config/application.py\", line 1075, in launch_instance\n", "    app.start()\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/ipykernel/kernelapp.py\", line 739, in start\n", "    self.io_loop.start()\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/tornado/platform/asyncio.py\", line 211, in start\n", "    self.asyncio_loop.run_forever()\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/asyncio/base_events.py\", line 608, in run_forever\n", "    self._run_once()\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/asyncio/base_events.py\", line 1936, in _run_once\n", "    handle._run()\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/asyncio/events.py\", line 84, in _run\n", "    self._context.run(self._callback, *self._args)\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/ipykernel/kernelbase.py\", line 545, in dispatch_queue\n", "    await self.process_one()\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/ipykernel/kernelbase.py\", line 534, in process_one\n", "    await dispatch(*args)\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/ipykernel/kernelbase.py\", line 437, in dispatch_shell\n", "    await result\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/ipykernel/ipkernel.py\", line 362, in execute_request\n", "    await super().execute_request(stream, ident, parent)\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/ipykernel/kernelbase.py\", line 778, in execute_request\n", "    reply_content = await reply_content\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/ipykernel/ipkernel.py\", line 449, in do_execute\n", "    res = shell.run_cell(\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/ipykernel/zmqshell.py\", line 549, in run_cell\n", "    return super().run_cell(*args, **kwargs)\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/IPython/core/interactiveshell.py\", line 3100, in run_cell\n", "    result = self._run_cell(\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/IPython/core/interactiveshell.py\", line 3155, in _run_cell\n", "    result = runner(coro)\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/IPython/core/async_helpers.py\", line 128, in _pseudo_sync_runner\n", "    coro.send(None)\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/IPython/core/interactiveshell.py\", line 3367, in run_cell_async\n", "    has_raised = await self.run_ast_nodes(code_ast.body, cell_name,\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/IPython/core/interactiveshell.py\", line 3612, in run_ast_nodes\n", "    if await self.run_code(code, result, async_=asy):\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/IPython/core/interactiveshell.py\", line 3672, in run_code\n", "    exec(code_obj, self.user_global_ns, self.user_ns)\n", "  File \"/var/folders/gf/nl47jt2n0w9dh5kzsz1rf1mc0000gn/T/ipykernel_78966/4033676583.py\", line 3, in <module>\n", "    logger.info(\"Source bounds:\", source_pcd.get_axis_aligned_bounding_box())\n", "Message: 'Source bounds:'\n", "Arguments: (AxisAlignedBoundingBox: min: (435222, 5.01082e+06, 140.046), max: (436794, 5.01254e+06, 153.136),)\n", "--- Logging error ---\n", "Traceback (most recent call last):\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/logging/__init__.py\", line 1110, in emit\n", "    msg = self.format(record)\n", "          ^^^^^^^^^^^^^^^^^^^\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/logging/__init__.py\", line 953, in format\n", "    return fmt.format(record)\n", "           ^^^^^^^^^^^^^^^^^^\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/logging/__init__.py\", line 687, in format\n", "    record.message = record.getMessage()\n", "                     ^^^^^^^^^^^^^^^^^^^\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/logging/__init__.py\", line 377, in getMessage\n", "    msg = msg % self.args\n", "          ~~~~^~~~~~~~~~~\n", "TypeError: not all arguments converted during string formatting\n", "Call stack:\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/runpy.py\", line 198, in _run_module_as_main\n", "    return _run_code(code, main_globals, None,\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/runpy.py\", line 88, in _run_code\n", "    exec(code, run_globals)\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/ipykernel_launcher.py\", line 18, in <module>\n", "    app.launch_new_instance()\n", "  File \"/Users/<USER>/.local/lib/python3.11/site-packages/traitlets/config/application.py\", line 1075, in launch_instance\n", "    app.start()\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/ipykernel/kernelapp.py\", line 739, in start\n", "    self.io_loop.start()\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/tornado/platform/asyncio.py\", line 211, in start\n", "    self.asyncio_loop.run_forever()\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/asyncio/base_events.py\", line 608, in run_forever\n", "    self._run_once()\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/asyncio/base_events.py\", line 1936, in _run_once\n", "    handle._run()\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/asyncio/events.py\", line 84, in _run\n", "    self._context.run(self._callback, *self._args)\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/ipykernel/kernelbase.py\", line 545, in dispatch_queue\n", "    await self.process_one()\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/ipykernel/kernelbase.py\", line 534, in process_one\n", "    await dispatch(*args)\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/ipykernel/kernelbase.py\", line 437, in dispatch_shell\n", "    await result\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/ipykernel/ipkernel.py\", line 362, in execute_request\n", "    await super().execute_request(stream, ident, parent)\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/ipykernel/kernelbase.py\", line 778, in execute_request\n", "    reply_content = await reply_content\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/ipykernel/ipkernel.py\", line 449, in do_execute\n", "    res = shell.run_cell(\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/ipykernel/zmqshell.py\", line 549, in run_cell\n", "    return super().run_cell(*args, **kwargs)\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/IPython/core/interactiveshell.py\", line 3100, in run_cell\n", "    result = self._run_cell(\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/IPython/core/interactiveshell.py\", line 3155, in _run_cell\n", "    result = runner(coro)\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/IPython/core/async_helpers.py\", line 128, in _pseudo_sync_runner\n", "    coro.send(None)\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/IPython/core/interactiveshell.py\", line 3367, in run_cell_async\n", "    has_raised = await self.run_ast_nodes(code_ast.body, cell_name,\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/IPython/core/interactiveshell.py\", line 3612, in run_ast_nodes\n", "    if await self.run_code(code, result, async_=asy):\n", "  File \"/Users/<USER>/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/IPython/core/interactiveshell.py\", line 3672, in run_code\n", "    exec(code_obj, self.user_global_ns, self.user_ns)\n", "  File \"/var/folders/gf/nl47jt2n0w9dh5kzsz1rf1mc0000gn/T/ipykernel_78966/4033676583.py\", line 4, in <module>\n", "    logger.info(\"Target bounds:\", target_pcd.get_axis_aligned_bounding_box())\n", "Message: 'Target bounds:'\n", "Arguments: (AxisAlignedBoundingBox: min: (435267, 5.0109e+06, 152.849), max: (436720, 5.01246e+06, 161.661),)\n", "\n", "Ending Cell 26-----------------------------------------\n", "Executing Cell 27--------------------------------------\n", "Ending Cell 27-----------------------------------------\n", "Executing Cell 28--------------------------------------\n", "INFO:__main__:=== Performance Metrics ===\n", "\n", "INFO:__main__:RMSE: 32.843573\n", "\n", "INFO:__main__:Mean Distance: 23.740664\n", "\n", "INFO:__main__:Median Distance: 15.017401\n", "\n", "INFO:__main__:Std Distance: 22.695400\n", "\n", "INFO:__main__:Max Distance: 132.633699\n", "\n", "INFO:__main__:Min Distance: 0.193794\n", "\n", "INFO:__main__:Accuracy (< 0.01): 0.00%\n", "\n", "INFO:__main__:Accuracy (< 0.05): 0.00%\n", "\n", "INFO:__main__:Accuracy (< 0.1): 0.00%\n", "\n", "INFO:__main__:Saved aligned point cloud to:\n", "\n", "INFO:__main__:  PCD: ../../data/output_runs/alignment_testing/csf/trino_enel/trino_enel/icp_results/icp_aligned_source.pcd\n", "\n", "INFO:__main__:  PLY: ../../data/output_runs/alignment_testing/csf/trino_enel/trino_enel/icp_results/icp_aligned_source.ply\n", "\n", "INFO:__main__:Saved metadata to: ../../data/output_runs/alignment_testing/csf/trino_enel/trino_enel/icp_results/icp_alignment_metadata.json\n", "\n", "Ending Cell 28-----------------------------------------\n", "Executing Cell 29--------------------------------------\n", "Ending Cell 29-----------------------------------------\n", "Executing Cell 30--------------------------------------\n", "INFO:__main__:Logging results to MLflow...\n", "\n", "INFO:__main__:MLflow logging completed successfully.\n", "\n", "INFO:__main__:MLflow run ended.\n", "\n", "INFO:__main__:=== ICP Alignment Completed Successfully ===\n", "\n", "INFO:__main__:Results saved to: ../../data/output_runs/alignment_testing/csf/trino_enel/trino_enel/icp_results\n", "\n", "INFO:__main__:Execution time: 2.7674 seconds\n", "\n", "INFO:__main__:Final RMSE: 32.843573\n", "\n", "Ending Cell 30-----------------------------------------\n", "Executing Cell 31--------------------------------------\n", "INFO:__main__:Results exported:\n", "\n", "INFO:__main__:  Individual: ../../data/output_runs/alignment_testing/csf/results/icp_ransac_pmf_results.json\n", "\n", "INFO:__main__:  Master: ../../data/output_runs/alignment_testing/master_results.csv\n", "\n", "INFO:__main__:\n", "=== Test Completed ===\n", "\n", "INFO:__main__:Ground Method: ransac_pmf\n", "\n", "INFO:__main__:Alignment Method: icp\n", "\n", "INFO:__main__:Success: True\n", "\n", "INFO:__main__:RMSE: 32.843573151187655\n", "\n", "INFO:__main__:Time: 2.7674267292022705s\n", "\n", "INFO:__main__:Iterations: 49\n", "\n", "Ending Cell 31-----------------------------------------\n", "Executing Cell 32--------------------------------------\n", "Ending Cell 32-----------------------------------------\n", "Executing Cell 33--------------------------------------\n", "Ending Cell 33-----------------------------------------\n"]}], "source": ["# RANSAC+PMF + ICP Alignment Test\n", "!papermill 01_icp_alignment.ipynb \\\n", "    ../../data/output_runs/alignment_testing/ransac_pmf/01_icp_alignment_ransac_pmf.ipynb \\\n", "    -p ground_method \"ransac_pmf\" \\\n", "    -p site_name \"trino_enel\" \\\n", "    -p project_type \"trino_enel\" \\\n", "    --log-output \\\n", "    --kernel pytorch-geo-dev\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### RANSAC+PMF + Neural Network Alignment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# RANSAC+PMF + Neural Network Alignment Test\n", "!papermill 02_neural_network_alignment.ipynb \\\n", "    ../../data/output_runs/alignment_testing/ransac_pmf/02_neural_network_alignment_ransac_pmf.ipynb \\\n", "    -p ground_method \"ransac_pmf\" \\\n", "    -p site_name \"trino_enel\" \\\n", "    -p project_type \"trino_enel\" \\\n", "    --log-output \\\n", "    --kernel pytorch-geo-dev\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### RANSAC+PMF + Hybrid Alignment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# RANSAC+PMF + Hybrid Alignment Test\n", "!papermill 03_hybrid_alignment.ipynb \\\n", "    ../../data/output_runs/alignment_testing/ransac_pmf/03_hybrid_alignment_ransac_pmf.ipynb \\\n", "    -p ground_method \"ransac_pmf\" \\\n", "    -p site_name \"trino_enel\" \\\n", "    -p project_type \"trino_enel\" \\\n", "    --log-output \\\n", "    --kernel pytorch-geo-dev\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Results Summary"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Total tests completed: 9\n", "\n", "Results summary:\n", "  ground_method alignment_method  success  final_rmse  convergence_time\n", "0           csf              icp    False      23.399             2.629\n", "1           pmf              icp     True      11.250             0.435\n", "2        ransac              icp    False      18.309             2.093\n", "3    ransac_pmf              icp    False      18.319             2.102\n", "4           csf              icp     True      34.335             1.965\n", "5           csf              icp     True      34.335             2.030\n", "6           pmf              icp    False      28.282             3.491\n", "7        ransac              icp     True      32.829             2.331\n", "8    ransac_pmf              icp     True      32.844             2.767\n", "\n", "Success rates:\n", "ground_method  alignment_method\n", "csf            icp                 0.666667\n", "pmf            icp                 0.500000\n", "ransac         icp                 0.500000\n", "ransac_pmf     icp                 0.500000\n", "Name: success, dtype: float64\n"]}], "source": ["# Check execution results\n", "import pandas as pd\n", "from pathlib import Path\n", "\n", "# Look for master results file\n", "results_file = Path(\"../../data/output_runs/alignment_testing/master_results.csv\")\n", "\n", "if results_file.exists():\n", "    df = pd.read_csv(results_file)\n", "    print(f\"Total tests completed: {len(df)}\")\n", "    print(\"\\nResults summary:\")\n", "    print(df[['ground_method', 'alignment_method', 'success', 'final_rmse', 'convergence_time']].round(3))\n", "    \n", "    # Success rate by method\n", "    print(\"\\nSuccess rates:\")\n", "    success_summary = df.groupby(['ground_method', 'alignment_method'])['success'].mean()\n", "    print(success_summary)\n", "else:\n", "    print(\"No master results file found yet. Run the tests above first.\")\n", "    \n", "    # Check individual result files\n", "    results_dir = Path(\"../../data/output_runs/alignment_testing\")\n", "    if results_dir.exists():\n", "        print(\"\\nAvailable result directories:\")\n", "        for method_dir in results_dir.iterdir():\n", "            if method_dir.is_dir():\n", "                result_files = list(method_dir.glob(\"*.ipynb\"))\n", "                print(f\"  {method_dir.name}: {len(result_files)} notebooks\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}