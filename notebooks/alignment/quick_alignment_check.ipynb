{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Quick ICP Alignment Check\n", "\n", "Minimal demonstration to check if ICP alignment is working properly."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import open3d as o3d\n", "import matplotlib.pyplot as plt\n", "from pathlib import Path\n", "from scipy.spatial import cKDTree\n", "\n", "# Load the results from previous run\n", "ground_method = \"csf\"\n", "output_dir = Path(\"../../data/output_runs/alignment_testing\") / ground_method\n", "\n", "print(f\"Checking results in: {output_dir}\")\n", "print(f\"Files available: {list(output_dir.glob('*')) if output_dir.exists() else 'Directory not found'}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load original and aligned point clouds\n", "source_file = Path(f\"../../data/processed/trino_enel/ground_segmentation/{ground_method}/trino_enel_nonground.ply\")\n", "target_file = Path(\"../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\")\n", "aligned_file = output_dir / f\"icp_aligned_{ground_method}.ply\"\n", "\n", "print(f\"Source exists: {source_file.exists()}\")\n", "print(f\"Target exists: {target_file.exists()}\")\n", "print(f\"Aligned exists: {aligned_file.exists()}\")\n", "\n", "if source_file.exists() and target_file.exists():\n", "    source_pcd = o3d.io.read_point_cloud(str(source_file))\n", "    target_pcd = o3d.io.read_point_cloud(str(target_file))\n", "    \n", "    source_points = np.asarray(source_pcd.points)\n", "    target_points = np.asarray(target_pcd.points)\n", "    \n", "    print(f\"\\nLoaded:\")\n", "    print(f\"  Source: {source_points.shape[0]:,} points\")\n", "    print(f\"  Target: {target_points.shape[0]:,} points\")\n", "    \n", "    if aligned_file.exists():\n", "        aligned_pcd = o3d.io.read_point_cloud(str(aligned_file))\n", "        aligned_points = np.asarray(aligned_pcd.points)\n", "        print(f\"  Aligned: {aligned_points.shape[0]:,} points\")\n", "    else:\n", "        print(\"  Aligned: Not found - using source for comparison\")\n", "        aligned_points = source_points\n", "else:\n", "    print(\"ERROR: Could not load point clouds\")\n", "    source_points = target_points = aligned_points = None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Quick metrics calculation\n", "if source_points is not None and target_points is not None:\n", "    print(\"\\n=== QUICK ALIGNMENT ASSESSMENT ===\")\n", "    \n", "    # Calculate distances before and after alignment\n", "    tree = cKDTree(target_points)\n", "    \n", "    # Before alignment\n", "    distances_before, _ = tree.query(source_points)\n", "    rmse_before = np.sqrt(np.mean(distances_before**2))\n", "    \n", "    # After alignment\n", "    distances_after, _ = tree.query(aligned_points)\n", "    rmse_after = np.sqrt(np.mean(distances_after**2))\n", "    \n", "    print(f\"\\nRMSE Comparison:\")\n", "    print(f\"  Before alignment: {rmse_before:.2f} m\")\n", "    print(f\"  After alignment:  {rmse_after:.2f} m\")\n", "    print(f\"  Improvement:      {rmse_before - rmse_after:.2f} m ({((rmse_before - rmse_after)/rmse_before*100):.1f}%)\")\n", "    \n", "    # Basic statistics\n", "    print(f\"\\nAfter Alignment Stats:\")\n", "    print(f\"  Mean distance:    {np.mean(distances_after):.2f} m\")\n", "    print(f\"  Median distance:  {np.median(distances_after):.2f} m\")\n", "    print(f\"  Std distance:     {np.std(distances_after):.2f} m\")\n", "    print(f\"  Max distance:     {np.max(distances_after):.2f} m\")\n", "    \n", "    # Accuracy metrics\n", "    acc_1cm = np.mean(distances_after < 0.01) * 100\n", "    acc_5cm = np.mean(distances_after < 0.05) * 100\n", "    acc_10cm = np.mean(distances_after < 0.1) * 100\n", "    acc_1m = np.mean(distances_after < 1.0) * 100\n", "    \n", "    print(f\"\\nAccuracy:\")\n", "    print(f\"  < 1cm:   {acc_1cm:.1f}%\")\n", "    print(f\"  < 5cm:   {acc_5cm:.1f}%\")\n", "    print(f\"  < 10cm:  {acc_10cm:.1f}%\")\n", "    print(f\"  < 1m:    {acc_1m:.1f}%\")\n", "    \n", "    # Assessment\n", "    if rmse_after < rmse_before:\n", "        if rmse_after < 1.0:\n", "            quality = \"EXCELLENT - Very good alignment\"\n", "        elif rmse_after < 5.0:\n", "            quality = \"GOOD - Reasonable alignment\"\n", "        elif rmse_after < 20.0:\n", "            quality = \"FAIR - Some improvement but needs work\"\n", "        else:\n", "            quality = \"POOR - Minimal improvement\"\n", "    else:\n", "        quality = \"FAILED - No improvement or worse\"\n", "    \n", "    print(f\"\\nOVERALL ASSESSMENT: {quality}\")\n", "else:\n", "    print(\"Cannot calculate metrics - missing point clouds\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Minimal visualization\n", "if source_points is not None and target_points is not None:\n", "    print(\"\\nCreating minimal visualization...\")\n", "    \n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))\n", "    \n", "    # Downsample for visualization\n", "    n_vis = min(1000, len(source_points))\n", "    source_idx = np.random.choice(len(source_points), n_vis, replace=False)\n", "    target_idx = np.random.choice(len(target_points), min(1000, len(target_points)), replace=False)\n", "    aligned_idx = np.random.choice(len(aligned_points), n_vis, replace=False)\n", "    \n", "    # Before alignment\n", "    ax1.scatter(source_points[source_idx, 0], source_points[source_idx, 1], \n", "               c='red', s=1, alpha=0.6, label='Source')\n", "    ax1.scatter(target_points[target_idx, 0], target_points[target_idx, 1], \n", "               c='green', s=1, alpha=0.6, label='Target')\n", "    ax1.set_title(f'Before Alignment\\nRMSE: {rmse_before:.2f}m')\n", "    ax1.legend()\n", "    ax1.grid(True, alpha=0.3)\n", "    ax1.set_aspect('equal')\n", "    \n", "    # After alignment\n", "    ax2.scatter(aligned_points[aligned_idx, 0], aligned_points[aligned_idx, 1], \n", "               c='blue', s=1, alpha=0.6, label='Aligned')\n", "    ax2.scatter(target_points[target_idx, 0], target_points[target_idx, 1], \n", "               c='green', s=1, alpha=0.6, label='Target')\n", "    ax2.set_title(f'After Alignment\\nRMSE: {rmse_after:.2f}m')\n", "    ax2.legend()\n", "    ax2.grid(True, alpha=0.3)\n", "    ax2.set_aspect('equal')\n", "    \n", "    plt.suptitle(f'ICP Alignment Results - {ground_method.upper()}', fontsize=14, fontweight='bold')\n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Distance histogram\n", "    plt.figure(figsize=(10, 4))\n", "    \n", "    plt.subplot(1, 2, 1)\n", "    plt.hist(distances_before, bins=50, alpha=0.7, color='red', label='Before')\n", "    plt.axvline(rmse_before, color='red', linestyle='--', label=f'RMSE: {rmse_before:.2f}m')\n", "    plt.xlabel('Distance (m)')\n", "    plt.ylabel('Frequency')\n", "    plt.title('Before Alignment')\n", "    plt.legend()\n", "    plt.grid(True, alpha=0.3)\n", "    \n", "    plt.subplot(1, 2, 2)\n", "    plt.hist(distances_after, bins=50, alpha=0.7, color='blue', label='After')\n", "    plt.axvline(rmse_after, color='blue', linestyle='--', label=f'RMSE: {rmse_after:.2f}m')\n", "    plt.xlabel('Distance (m)')\n", "    plt.ylabel('Frequency')\n", "    plt.title('After Alignment')\n", "    plt.legend()\n", "    plt.grid(True, alpha=0.3)\n", "    \n", "    plt.suptitle('Distance Distribution Comparison', fontsize=14, fontweight='bold')\n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    print(f\"\\n=== RECOMMENDATION ===\")\n", "    if rmse_after > 10:\n", "        print(\"❌ Alignment quality is poor. Consider:\")\n", "        print(\"   - Checking point cloud coordinate systems\")\n", "        print(\"   - Improving initial pre-alignment\")\n", "        print(\"   - Using different ground segmentation method\")\n", "        print(\"   - Adjusting ICP parameters (iterations, tolerance)\")\n", "    elif rmse_after > 5:\n", "        print(\"⚠️  Alignment shows some improvement but could be better. Consider:\")\n", "        print(\"   - Fine-tuning ICP parameters\")\n", "        print(\"   - Better pre-alignment strategy\")\n", "        print(\"   - Checking for outliers in point clouds\")\n", "    else:\n", "        print(\"✅ Alignment quality is good! You can proceed with:\")\n", "        print(\"   - Further analysis and validation\")\n", "        print(\"   - Testing other ground segmentation methods\")\n", "        print(\"   - Comparing with neural network alignment\")\n", "else:\n", "    print(\"Cannot create visualization - missing point clouds\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}