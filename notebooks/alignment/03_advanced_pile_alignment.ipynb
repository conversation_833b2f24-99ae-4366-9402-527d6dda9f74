{
 "cells": [
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# Advanced Pile-Based Alignment\n",
    "\n",
    "Use advanced IFC pile extraction for robust point cloud alignment.\n",
    "\n",
    "**Strategy:**\n",
    "1. Load pile coordinates from advanced IFC extraction\n",
    "2. Detect corresponding pile locations in point cloud\n",
    "3. Calculate transformation using pile correspondences\n",
    "4. Apply coordinate system correction and alignment\n",
    "\n",
    "**Advantages:**\n",
    "- Uses sophisticated IFC coordinate extraction\n",
    "- Handles UTM coordinate system transformations\n",
    "- Robust error handling and validation\n",
    "- Comprehensive alignment quality metrics\n",
    "\n",
    "**Author**: Preetam Bali<PERSON>palli  \n",
    "**Date**: July 2025"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {
    "tags": [
     "parameters"
    ]
   },
   "outputs": [],
   "source": [
    "# Parameters\n",
    "ground_method = \"csf\"\n",
    "site_name = \"trino_enel\"\n",
    "pile_csv = \"../../data/processed/trino_enel/advanced_pile_metadata/advanced_pile_coordinates.csv\"\n",
    "output_dir = \"../../data/output_runs/advanced_pile_alignment\"\n",
    "pile_search_radius = 3.0  # meters to search for piles in point cloud\n",
    "min_pile_height = 1.0  # minimum pile height above ground\n",
    "max_pile_height = 15.0  # maximum pile height\n",
    "max_piles_to_use = 50  # limit for performance and visualization\n",
    "correspondence_max_distance = 200.0  # maximum distance for pile correspondence"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "import numpy as np\n",
    "import open3d as o3d\n",
    "import matplotlib.pyplot as plt\n",
    "import pandas as pd\n",
    "import json\n",
    "from pathlib import Path\n",
    "from scipy.spatial import cKDTree\n",
    "from sklearn.cluster import DBSCAN\n",
    "import seaborn as sns\n",
    "\n",
    "# Setup\n",
    "output_path = Path(output_dir) / ground_method\n",
    "output_path.mkdir(parents=True, exist_ok=True)\n",
    "\n",
    "print(\"🏗️ ADVANCED PILE-BASED ALIGNMENT\")\n",
    "print(f\"Site: {site_name}\")\n",
    "print(f\"Ground Method: {ground_method}\")\n",
    "print(f\"Pile CSV: {pile_csv}\")\n",
    "print(f\"Output: {output_path}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 1. Load Advanced Pile Coordinates"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Load pile coordinates from advanced extraction\n",
    "print(f\"\\n📊 Loading advanced pile coordinates...\")\n",
    "\n",
    "csv_path = Path(pile_csv)\n",
    "if csv_path.exists():\n",
    "    # Load CSV\n",
    "    pile_df = pd.read_csv(csv_path)\n",
    "    print(f\"✅ Loaded {len(pile_df):,} piles from advanced extraction\")\n",
    "    \n",
    "    # Display pile information\n",
    "    print(f\"\\n📋 Pile Dataset Information:\")\n",
    "    print(f\"   Columns: {list(pile_df.columns)}\")\n",
    "    \n",
    "    # Check for different pile types\n",
    "    if 'Name' in pile_df.columns:\n",
    "        pile_types = pile_df['Name'].value_counts()\n",
    "        print(f\"   Pile types found: {len(pile_types)}\")\n",
    "        for pile_type, count in pile_types.head().items():\n",
    "            print(f\"      {pile_type}: {count:,} piles\")\n",
    "    \n",
    "    # Filter for tracker piles if multiple types exist\n",
    "    if 'Name' in pile_df.columns:\n",
    "        tracker_mask = pile_df['Name'].str.contains('TRPL_Tracker Pile', na=False)\n",
    "        if tracker_mask.any():\n",
    "            pile_df_filtered = pile_df[tracker_mask].copy()\n",
    "            print(f\"   Filtered to {len(pile_df_filtered):,} tracker piles\")\n",
    "        else:\n",
    "            pile_df_filtered = pile_df.copy()\n",
    "            print(f\"   Using all {len(pile_df_filtered):,} piles (no tracker pile filter applied)\")\n",
    "    else:\n",
    "        pile_df_filtered = pile_df.copy()\n",
    "        print(f\"   Using all {len(pile_df_filtered):,} piles\")\n",
    "    \n",
    "    # Extract coordinates\n",
    "    if all(col in pile_df_filtered.columns for col in ['X', 'Y', 'Z']):\n",
    "        pile_coords_ifc = pile_df_filtered[['X', 'Y', 'Z']].values\n",
    "        \n",
    "        # Limit number of piles for performance\n",
    "        if len(pile_coords_ifc) > max_piles_to_use:\n",
    "            # Sample evenly across the site\n",
    "            indices = np.linspace(0, len(pile_coords_ifc)-1, max_piles_to_use, dtype=int)\n",
    "            pile_coords_ifc = pile_coords_ifc[indices]\n",
    "            pile_df_subset = pile_df_filtered.iloc[indices]\n",
    "            print(f\"📉 Sampled {len(pile_coords_ifc)} piles for performance\")\n",
    "        else:\n",
    "            pile_df_subset = pile_df_filtered\n",
    "        \n",
    "        print(f\"\\n📍 IFC Pile coordinate ranges:\")\n",
    "        print(f\"   X: {pile_coords_ifc[:,0].min():.2f} to {pile_coords_ifc[:,0].max():.2f} (range: {pile_coords_ifc[:,0].max()-pile_coords_ifc[:,0].min():.2f}m)\")\n",
    "        print(f\"   Y: {pile_coords_ifc[:,1].min():.2f} to {pile_coords_ifc[:,1].max():.2f} (range: {pile_coords_ifc[:,1].max()-pile_coords_ifc[:,1].min():.2f}m)\")\n",
    "        print(f\"   Z: {pile_coords_ifc[:,2].min():.2f} to {pile_coords_ifc[:,2].max():.2f} (range: {pile_coords_ifc[:,2].max()-pile_coords_ifc[:,2].min():.2f}m)\")\n",
    "        \n",
    "        # Analyze coordinate system\n",
    "        x_mean, y_mean, z_mean = np.mean(pile_coords_ifc, axis=0)\n",
    "        print(f\"\\n🌍 Coordinate System Analysis:\")\n",
    "        print(f\"   Mean coordinates: [{x_mean:.1f}, {y_mean:.1f}, {z_mean:.1f}]\")\n",
    "        \n",
    "        if 100000 < x_mean < 900000 and 1000000 < y_mean < 10000000:\n",
    "            print(f\"   ✅ Coordinates appear to be in UTM format\")\n",
    "        else:\n",
    "            print(f\"   ⚠️  Coordinates may not be standard UTM\")\n",
    "        \n",
    "        if z_mean > 100:\n",
    "            print(f\"   ✅ Z-coordinates appear to be absolute elevation (~{z_mean:.0f}m above sea level)\")\n",
    "            print(f\"   🎯 This confirms the 155m offset seen in diagnostics!\")\n",
    "        else:\n",
    "            print(f\"   ⚠️  Z-coordinates appear to be relative elevation\")\n",
    "        \n",
    "        # Store pile info\n",
    "        pile_info = pile_df_subset.to_dict('records')\n",
    "        \n",
    "    else:\n",
    "        print(f\"❌ Missing coordinate columns in pile data\")\n",
    "        pile_coords_ifc = None\n",
    "        pile_info = None\n",
    "        \n",
    "else:\n",
    "    print(f\"❌ Advanced pile CSV not found: {csv_path}\")\n",
    "    print(f\"   Please run the advanced pile extraction notebook first\")\n",
    "    pile_coords_ifc = None\n",
    "    pile_info = None"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 2. Load Point Cloud and Detect Pile Locations"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Load point cloud\n",
    "source_file = Path(f\"../../data/processed/{site_name}/ground_segmentation/{ground_method}/trino_enel_nonground.ply\")\n",
    "\n",
    "print(f\"\\n📁 Loading point cloud...\")\n",
    "print(f\"Source: {source_file.exists()} - {source_file}\")\n",
    "\n",
    "if source_file.exists():\n",
    "    source_pcd = o3d.io.read_point_cloud(str(source_file))\n",
    "    source_points = np.asarray(source_pcd.points)\n",
    "    \n",
    "    print(f\"✅ Loaded {source_points.shape[0]:,} points\")\n",
    "    print(f\"   X range: {source_points[:,0].min():.2f} to {source_points[:,0].max():.2f}\")\n",
    "    print(f\"   Y range: {source_points[:,1].min():.2f} to {source_points[:,1].max():.2f}\")\n",
    "    print(f\"   Z range: {source_points[:,2].min():.2f} to {source_points[:,2].max():.2f}\")\n",
    "    \n",
    "    # Estimate ground level\n",
    "    ground_level = np.percentile(source_points[:, 2], 10)  # 10th percentile as ground\n",
    "    print(f\"   Estimated ground level: {ground_level:.2f}m\")\n",
    "    \n",
    "    # Compare coordinate systems\n",
    "    if pile_coords_ifc is not None:\n",
    "        pc_centroid = np.mean(source_points, axis=0)\n",
    "        ifc_centroid = np.mean(pile_coords_ifc, axis=0)\n",
    "        \n",
    "        print(f\"\\n🔍 Coordinate System Comparison:\")\n",
    "        print(f\"   Point Cloud centroid: [{pc_centroid[0]:.1f}, {pc_centroid[1]:.1f}, {pc_centroid[2]:.1f}]\")\n",
    "        print(f\"   IFC Pile centroid:    [{ifc_centroid[0]:.1f}, {ifc_centroid[1]:.1f}, {ifc_centroid[2]:.1f}]\")\n",
    "        \n",
    "        offset = ifc_centroid - pc_centroid\n",
    "        distance = np.linalg.norm(offset)\n",
    "        print(f\"   Offset: [{offset[0]:.1f}, {offset[1]:.1f}, {offset[2]:.1f}]\")\n",
    "        print(f\"   Distance: {distance:.1f}m\")\n",
    "        \n",
    "        if distance > 1000:\n",
    "            print(f\"   ❌ MAJOR coordinate system mismatch (>1000m)\")\n",
    "        elif distance > 100:\n",
    "            print(f\"   ⚠️  SIGNIFICANT coordinate offset (100-1000m)\")\n",
    "        else:\n",
    "            print(f\"   ✅ REASONABLE coordinate proximity (<100m)\")\n",
    "    \n",
    "else:\n",
    "    print(f\"❌ Point cloud file not found\")\n",
    "    source_points = None\n",
    "    ground_level = 0"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 3. Detect Pile Locations in Point Cloud"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def detect_pile_locations_in_pointcloud(points, ground_level, search_radius=3.0, min_height=1.0, max_height=15.0):\n",
    "    \"\"\"\n",
    "    Detect potential pile locations in point cloud using clustering\n",
    "    \"\"\"\n",
    "    print(f\"\\n🔍 Detecting pile locations in point cloud...\")\n",
    "    \n",
    "    # Filter points above ground level\n",
    "    above_ground = points[points[:, 2] > ground_level + min_height]\n",
    "    below_max = above_ground[above_ground[:, 2] < ground_level + max_height]\n",
    "    \n",
    "    print(f\"   Points above ground ({min_height}m-{max_height}m): {len(below_max):,}\")\n",
    "    \n",
    "    if len(below_max) < 10:\n",
    "        print(f\"❌ Too few points for pile detection\")\n",
    "        return None, None\n",
    "    \n",
    "    # Cluster points to find pile-like structures\n",
    "    clustering = DBSCAN(eps=search_radius, min_samples=5)\n",
    "    cluster_labels = clustering.fit_predict(below_max[:, :2])  # Only XY for clustering\n",
    "    \n",
    "    # Find cluster centers\n",
    "    pile_candidates = []\n",
    "    unique_labels = set(cluster_labels)\n",
    "    \n",
    "    for label in unique_labels:\n",
    "        if label == -1:  # Noise points\n",
    "            continue\n",
    "            \n",
    "        cluster_points = below_max[cluster_labels == label]\n",
    "        \n",
    "        # Calculate cluster center\n",
    "        center_x = np.mean(cluster_points[:, 0])\n",
    "        center_y = np.mean(cluster_points[:, 1])\n",
    "        center_z = np.mean(cluster_points[:, 2])\n",
    "        \n",
    "        # Check if cluster looks like a pile (vertical structure)\n",
    "        height_range = cluster_points[:, 2].max() - cluster_points[:, 2].min()\n",
    "        xy_spread = np.std(cluster_points[:, :2], axis=0).mean()\n",
    "        \n",
    "        # Pile criteria: reasonable height, not too spread out horizontally\n",
    "        if height_range > min_height and xy_spread < search_radius:\n",
    "            pile_candidates.append({\n",
    "                'center': np.array([center_x, center_y, center_z]),\n",
    "                'points_count': len(cluster_points),\n",
    "                'height_range': height_range,\n",
    "                'xy_spread': xy_spread,\n",
    "                'cluster_id': label\n",
    "            })\n",
    "    \n",
    "    print(f\"   Found {len(pile_candidates)} potential pile locations\")\n",
    "    \n",
    "    if pile_candidates:\n",
    "        pile_centers = np.array([p['center'] for p in pile_candidates])\n",
    "        \n",
    "        # Show some statistics\n",
    "        print(f\"   Pile detection statistics:\")\n",
    "        points_counts = [p['points_count'] for p in pile_candidates]\n",
    "        height_ranges = [p['height_range'] for p in pile_candidates]\n",
    "        print(f\"      Points per pile: {np.mean(points_counts):.1f} ± {np.std(points_counts):.1f}\")\n",
    "        print(f\"      Height ranges: {np.mean(height_ranges):.2f} ± {np.std(height_ranges):.2f}m\")\n",
    "        \n",
    "        return pile_centers, pile_candidates\n",
    "    else:\n",
    "        return None, None\n",
    "\n",
    "# Detect piles in point cloud\n",
    "if source_points is not None:\n",
    "    pile_coords_pc, pile_candidates = detect_pile_locations_in_pointcloud(\n",
    "        source_points, ground_level, pile_search_radius, min_pile_height, max_pile_height\n",
    "    )\n",
    "    \n",
    "    if pile_coords_pc is not None:\n",
    "        print(f\"\\n📍 Point Cloud pile coordinate ranges:\")\n",
    "        print(f\"   X: {pile_coords_pc[:,0].min():.2f} to {pile_coords_pc[:,0].max():.2f}\")\n",
    "        print(f\"   Y: {pile_coords_pc[:,1].min():.2f} to {pile_coords_pc[:,1].max():.2f}\")\n",
    "        print(f\"   Z: {pile_coords_pc[:,2].min():.2f} to {pile_coords_pc[:,2].max():.2f}\")\n",
    "else:\n",
    "    pile_coords_pc = None\n",
    "    pile_candidates = None"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 4. Find Pile Correspondences and Calculate Transformation"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def find_pile_correspondences_advanced(ifc_piles, pc_piles, max_distance=200.0):\n",
    "    \"\"\"\n",
    "    Find correspondences between IFC pile locations and point cloud pile detections\n",
    "    with advanced matching strategies\n",
    "    \"\"\"\n",
    "    print(f\"\\n🔗 Finding pile correspondences (advanced method)...\")\n",
    "    \n",
    "    if ifc_piles is None or pc_piles is None:\n",
    "        print(f\"❌ Cannot find correspondences - missing pile data\")\n",
    "        return None, None\n",
    "    \n",
    "    print(f\"   IFC piles: {len(ifc_piles)}\")\n",
    "    print(f\"   Point cloud piles: {len(pc_piles)}\")\n",
    "    print(f\"   Max correspondence distance: {max_distance}m\")\n",
    "    \n",
    "    # Strategy 1: Direct correspondence (if coordinate systems are similar)\n",
    "    correspondences_direct = []\n",
    "    for i, ifc_pile in enumerate(ifc_piles):\n",
    "        distances = np.linalg.norm(pc_piles - ifc_pile, axis=1)\n",
    "        closest_idx = np.argmin(distances)\n",
    "        closest_distance = distances[closest_idx]\n",
    "        \n",
    "        if closest_distance < max_distance:\n",
    "            correspondences_direct.append({\n",
    "                'ifc_idx': i,\n",
    "                'pc_idx': closest_idx,\n",
    "                'ifc_coords': ifc_pile,\n",
    "                'pc_coords': pc_piles[closest_idx],\n",
    "                'distance': closest_distance\n",
    "            })\n",
    "    \n",
    "    print(f\"   Direct correspondences found: {len(correspondences_direct)}\")\n",
    "    \n",
    "    # Strategy 2: Pattern-based correspondence (if direct fails)\n",
    "    if len(correspondences_direct) < 3:\n",
    "        print(f\"   Trying pattern-based correspondence...\")\n",
    "        \n",
    "        # Center both point sets\n",
    "        ifc_centered = ifc_piles - np.mean(ifc_piles, axis=0)\n",
    "        pc_centered = pc_piles - np.mean(pc_piles, axis=0)\n",
    "        \n",
    "        # Try to match patterns by finding similar relative positions\n",
    "        correspondences_pattern = []\n",
    "        \n",
    "        # Use a subset for pattern matching to avoid combinatorial explosion\n",
    "        max_pattern_piles = min(20, len(ifc_piles), len(pc_piles))\n",
    "        \n",
    "        for i in range(min(max_pattern_piles, len(ifc_centered))):\n",
    "            ifc_pile = ifc_centered[i]\n",
    "            \n",
    "            # Find closest in pattern space\n",
    "            distances = np.linalg.norm(pc_centered - ifc_pile, axis=1)\n",
    "            closest_idx = np.argmin(distances)\n",
    "            closest_distance = distances[closest_idx]\n",
    "            \n",
    "            # Use more lenient distance for pattern matching\n",
    "            if closest_distance < max_distance * 2:\n",
    "                correspondences_pattern.append({\n",
    "                    'ifc_idx': i,\n",
    "                    'pc_idx': closest_idx,\n",
    "                    'ifc_coords': ifc_piles[i],\n",
    "                    'pc_coords': pc_piles[closest_idx],\n",
    "                    'distance': closest_distance,\n",
    "                    'pattern_distance': closest_distance\n",
    "                })\n",
    "        \n",
    "        print(f\"   Pattern-based correspondences found: {len(correspondences_pattern)}\")\n",
    "        \n",
    "        # Use pattern-based if it found more correspondences\n",
    "        if len(correspondences_pattern) > len(correspondences_direct):\n",
    "            correspondences = correspondences_pattern\n",
    "            print(f\"   Using pattern-based correspondences\")\n",
    "        else:\n",
    "            correspondences = correspondences_direct\n",
    "            print(f\"   Using direct correspondences\")\n",
    "    else:\n",
    "        correspondences = correspondences_direct\n",
    "        print(f\"   Using direct correspondences\")\n",
    "    \n",
    "    print(f\"\\n✅ Final correspondences: {len(correspondences)}\")\n",
    "    \n",
    "    if len(correspondences) >= 3:\n",
    "        # Extract coordinate pairs\n",
    "        ifc_coords = np.array([c['ifc_coords'] for c in correspondences])\n",
    "        pc_coords = np.array([c['pc_coords'] for c in correspondences])\n",
    "        \n",
    "        # Show correspondence quality\n",
    "        distances = [c['distance'] for c in correspondences]\n",
    "        print(f\"   Correspondence distances: {np.mean(distances):.2f} ± {np.std(distances):.2f}m\")\n",
    "        print(f\"   Min distance: {np.min(distances):.2f}m, Max distance: {np.max(distances):.2f}m\")\n",
    "        \n",
    "        return ifc_coords, pc_coords\n",
    "    else:\n",
    "        print(f\"❌ Need at least 3 correspondences for alignment (found {len(correspondences)})\")\n",
    "        return None, None\n",
    "\n",
    "# Find correspondences\n",
    "if pile_coords_ifc is not None and pile_coords_pc is not None:\n",
    "    ifc_anchor_points, pc_anchor_points = find_pile_correspondences_advanced(\n",
    "        pile_coords_ifc, pile_coords_pc, correspondence_max_distance\n",
    "    )\n",
    "else:\n",
    "    print(f\"\\n⚠️  Cannot proceed with pile-based alignment:\")\n",
    "    if pile_coords_ifc is None:\n",
    "        print(f\"   - No pile coordinates from advanced IFC extraction\")\n",
    "    if pile_coords_pc is None:\n",
    "        print(f\"   - No pile locations detected in point cloud\")\n",
    "    \n",
    "    ifc_anchor_points = None\n",
    "    pc_anchor_points = None"
   ]
  }
 ],
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "mimetype": "text/x-python",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.11.11"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 4
}
