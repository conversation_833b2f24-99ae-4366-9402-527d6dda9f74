# Install papermill if needed
#! pip install papermill 

# Setup environment and create output directories
import os
from pathlib import Path
import subprocess
from datetime import datetime

# Create output directory structure
output_base = Path("../../../data/output_runs/alignment_testing")
ground_methods = ["csf", "pmf", "ransac", "ransac_pmf"]

for method in ground_methods:
    method_dir = output_base / method
    method_dir.mkdir(parents=True, exist_ok=True)
    (method_dir / "results").mkdir(exist_ok=True)

print(f"Output directories created at: {output_base}")
print(f"Testing will begin at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
print("\nDirectory structure:")
for method in ground_methods:
    print(f"  {output_base / method}")

def run_icp_tests():
    """Run ICP tests with method-specific parameters"""
    print("Running ICP tests...")
    tests = {
        "csf": {
            "voxel_size": 0.05,
            "max_iterations": 30,
            "tolerance": 1e-6
        },
        "pmf": {
            "voxel_size": 0.1,
            "max_iterations": 50,
            "tolerance": 1e-5
        },
        "ransac": {
            "voxel_size": 0.02,
            "max_iterations": 100,
            "tolerance": 1e-7
        },
        "ransac_pmf": {
            "voxel_size": 0.075,
            "max_iterations": 75,
            "tolerance": 1e-6
        }
    }
    
    for method, params in tests.items():
        !papermill 01_icp_alignment.ipynb \
            {output_base}/{method}/01_icp_alignment_{method}.ipynb \
            -p ground_method {method} \
            -p site_name "trino_enel" \
            -p voxel_size {params["voxel_size"]} \
            -p icp_max_iterations {params["max_iterations"]} \
            -p icp_tolerance {params["tolerance"]} \
            --log-output \
            --kernel pytorch-geo-dev
        
        print(f"Completed ICP + {method.upper()} with params: {params}")

# Run ICP tests
run_icp_tests()

def run_neural_tests():
    """Run NN tests with method-specific parameters"""
    tests = {
        "csf": {
            "learning_rate": 0.001,
            "batch_size": 32
        },
        "pmf": {
            "learning_rate": 0.0005,
            "batch_size": 64
        },
        "ransac": {
            "learning_rate": 0.002,
            "batch_size": 16
        },
        "ransac_pmf": {
            "learning_rate": 0.0015,
            "batch_size": 48
        }
    }
    
    for method, params in tests.items():
        !papermill 02_neural_network_alignment.ipynb \
            {output_base}/{method}/02_neural_alignment_{method}.ipynb \
            -p ground_method {method} \
            -p site_name "trino_enel" \
            -p learning_rate {params["learning_rate"]} \
            -p batch_size {params["batch_size"]} \
            --log-output \
            --kernel pytorch-geo-dev
        
        print(f"Completed Neural + {method.upper()} with params: {params}")

# Run neural network tests
run_neural_tests()

def run_hybrid_tests():
    """Run hybrid tests with combined parameters"""
    tests = {
        "csf": {
            "nn_learning_rate": 0.001,
            "icp_voxel_size": 0.05,
            "icp_iterations": 30
        },
        "pmf": {
            "nn_learning_rate": 0.0005,
            "icp_voxel_size": 0.1,
            "icp_iterations": 50
        },
        "ransac": {
            "nn_learning_rate": 0.002,
            "icp_voxel_size": 0.02,
            "icp_iterations": 100
        },
        "ransac_pmf": {
            "nn_learning_rate": 0.0015,
            "icp_voxel_size": 0.075,
            "icp_iterations": 75
        }
    }
    
    for method, params in tests.items():
        !papermill 03_hybrid_alignment.ipynb \
            {output_base}/alignment_testing/{method}/03_hybrid_alignment_{method}.ipynb \
            -p ground_method {method} \
            -p site_name "trino_enel" \
            -p learning_rate {params["nn_learning_rate"]} \
            -p voxel_size {params["icp_voxel_size"]} \
            -p icp_max_iterations {params["icp_iterations"]} \
            --log-output \
            --kernel pytorch-geo-dev
        
        print(f"Completed Hybrid + {method.upper()} with params: {params}")

# Run hybrid tests
run_hybrid_tests()





