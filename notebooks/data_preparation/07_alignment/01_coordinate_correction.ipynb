{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Coordinate Correction for Alignment\n", "\n", "This notebook applies coordinate corrections based on the diagnosis results to prepare datasets for alignment.\n", "\n", "**Key Corrections:**\n", "- Horizontal offset: 74.3m (X: +38.3m, Y: -63.6m)\n", "- Z-offset: 154.9m (different elevation references)\n", "- Align centroids and elevation references\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025"]}, {"cell_type": "code", "execution_count": null, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Parameters\n", "site_name = \"trino_enel\"\n", "ground_method = \"csf\"\n", "output_dir = \"../../data/processed/trino_enel/aligned_coordinates\"\n", "save_corrected_files = True\n", "\n", "# Correction values from diagnosis\n", "x_offset = 38.35  # Drone centroid - IFC centroid\n", "y_offset = -63.60\n", "z_offset = -154.87  # Align IFC to drone elevation reference"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Imports\n", "import numpy as np\n", "import open3d as o3d\n", "import matplotlib.pyplot as plt\n", "from pathlib import Path\n", "import json\n", "from datetime import datetime\n", "\n", "# Setup\n", "output_path = Path(output_dir)\n", "output_path.mkdir(parents=True, exist_ok=True)\n", "\n", "print(f\"🔧 COORDINATE CORRECTION\")\n", "print(f\"Site: {site_name}\")\n", "print(f\"Ground method: {ground_method}\")\n", "print(f\"Corrections: X={x_offset:.2f}m, Y={y_offset:.2f}m, Z={z_offset:.2f}m\")\n", "print(f\"Output: {output_path}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load Original Datasets"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load drone point cloud\n", "drone_file = Path(f\"../../data/processed/{site_name}/ground_segmentation/{ground_method}/trino_enel_nonground.ply\")\n", "drone_pcd = o3d.io.read_point_cloud(str(drone_file))\n", "drone_points_original = np.asarray(drone_pcd.points)\n", "\n", "print(f\"✅ Loaded drone point cloud: {drone_points_original.shape[0]:,} points\")\n", "print(f\"Original drone bounds:\")\n", "print(f\"  X: [{drone_points_original[:, 0].min():.2f}, {drone_points_original[:, 0].max():.2f}]\")\n", "print(f\"  Y: [{drone_points_original[:, 1].min():.2f}, {drone_points_original[:, 1].max():.2f}]\")\n", "print(f\"  Z: [{drone_points_original[:, 2].min():.2f}, {drone_points_original[:, 2].max():.2f}]\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load IFC point cloud\n", "ifc_file = Path(f\"../../data/processed/{site_name}/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\")\n", "ifc_pcd = o3d.io.read_point_cloud(str(ifc_file))\n", "ifc_points_original = np.asarray(ifc_pcd.points)\n", "\n", "print(f\"\\n✅ Loaded IFC point cloud: {ifc_points_original.shape[0]:,} points\")\n", "print(f\"Original IFC bounds:\")\n", "print(f\"  X: [{ifc_points_original[:, 0].min():.2f}, {ifc_points_original[:, 0].max():.2f}]\")\n", "print(f\"  Y: [{ifc_points_original[:, 1].min():.2f}, {ifc_points_original[:, 1].max():.2f}]\")\n", "print(f\"  Z: [{ifc_points_original[:, 2].min():.2f}, {ifc_points_original[:, 2].max():.2f}]\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Apply Coordinate Corrections"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Apply corrections\n", "print(f\"\\n🔧 APPLYING COORDINATE CORRECTIONS\")\n", "\n", "# Strategy: Keep drone coordinates as reference, adjust IFC to match\n", "# This preserves the drone's ground-relative Z coordinates\n", "\n", "# Drone: Keep original (it's our reference)\n", "drone_points_corrected = drone_points_original.copy()\n", "\n", "# IFC: Apply translation to align with drone\n", "ifc_correction = np.array([x_offset, y_offset, z_offset])\n", "ifc_points_corrected = ifc_points_original + ifc_correction\n", "\n", "print(f\"Applied correction to IFC: [{x_offset:.2f}, {y_offset:.2f}, {z_offset:.2f}]\")\n", "print(f\"Drone coordinates: Unchanged (reference frame)\")\n", "print(f\"IFC coordinates: Translated by correction vector\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Verify corrections\n", "print(f\"\\n📊 CORRECTED COORDINATE BOUNDS\")\n", "\n", "print(f\"\\nDrone (reference):\")\n", "print(f\"  X: [{drone_points_corrected[:, 0].min():.2f}, {drone_points_corrected[:, 0].max():.2f}]\")\n", "print(f\"  Y: [{drone_points_corrected[:, 1].min():.2f}, {drone_points_corrected[:, 1].max():.2f}]\")\n", "print(f\"  Z: [{drone_points_corrected[:, 2].min():.2f}, {drone_points_corrected[:, 2].max():.2f}]\")\n", "\n", "print(f\"\\nIFC (corrected):\")\n", "print(f\"  X: [{ifc_points_corrected[:, 0].min():.2f}, {ifc_points_corrected[:, 0].max():.2f}]\")\n", "print(f\"  Y: [{ifc_points_corrected[:, 1].min():.2f}, {ifc_points_corrected[:, 1].max():.2f}]\")\n", "print(f\"  Z: [{ifc_points_corrected[:, 2].min():.2f}, {ifc_points_corrected[:, 2].max():.2f}]\")\n", "\n", "# Calculate new centroids and offsets\n", "drone_centroid_new = drone_points_corrected.mean(axis=0)\n", "ifc_centroid_new = ifc_points_corrected.mean(axis=0)\n", "remaining_offset = drone_centroid_new - ifc_centroid_new\n", "\n", "print(f\"\\n🎯 ALIGNMENT VERIFICATION\")\n", "print(f\"Drone centroid: [{drone_centroid_new[0]:.2f}, {drone_centroid_new[1]:.2f}, {drone_centroid_new[2]:.2f}]\")\n", "print(f\"IFC centroid:   [{ifc_centroid_new[0]:.2f}, {ifc_centroid_new[1]:.2f}, {ifc_centroid_new[2]:.2f}]\")\n", "print(f\"Remaining offset: [{remaining_offset[0]:.2f}, {remaining_offset[1]:.2f}, {remaining_offset[2]:.2f}]\")\n", "print(f\"Offset magnitude: {np.linalg.norm(remaining_offset):.2f}m\")\n", "\n", "# Success criteria\n", "alignment_success = np.linalg.norm(remaining_offset) < 10.0  # Within 10m\n", "print(f\"Alignment success: {'✅ Yes' if alignment_success else '❌ No'} (target: <10m)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create before/after visualization\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "fig.suptitle('Coordinate Correction Results', fontsize=16)\n", "\n", "# Plot 1: Before correction (XY)\n", "axes[0, 0].scatter(drone_points_original[::10, 0], drone_points_original[::10, 1], \n", "                  c='blue', alpha=0.6, s=1, label='Drone')\n", "axes[0, 0].scatter(ifc_points_original[::100, 0], ifc_points_original[::100, 1], \n", "                  c='red', alpha=0.6, s=1, label='IFC')\n", "axes[0, 0].set_xlabel('X (UTM)')\n", "axes[0, 0].set_ylabel('Y (UTM)')\n", "axes[0, 0].set_title('Before Correction (XY)')\n", "axes[0, 0].legend()\n", "axes[0, 0].grid(True, alpha=0.3)\n", "axes[0, 0].axis('equal')\n", "\n", "# Plot 2: After correction (XY)\n", "axes[0, 1].scatter(drone_points_corrected[::10, 0], drone_points_corrected[::10, 1], \n", "                  c='blue', alpha=0.6, s=1, label='Drone')\n", "axes[0, 1].scatter(ifc_points_corrected[::100, 0], ifc_points_corrected[::100, 1], \n", "                  c='red', alpha=0.6, s=1, label='IFC (corrected)')\n", "axes[0, 1].set_xlabel('X (UTM)')\n", "axes[0, 1].set_ylabel('Y (UTM)')\n", "axes[0, 1].set_title('After Correction (XY)')\n", "axes[0, 1].legend()\n", "axes[0, 1].grid(True, alpha=0.3)\n", "axes[0, 1].axis('equal')\n", "\n", "# Plot 3: Before correction (XZ)\n", "axes[1, 0].scatter(drone_points_original[::10, 0], drone_points_original[::10, 2], \n", "                  c='blue', alpha=0.6, s=1, label='Drone')\n", "axes[1, 0].scatter(ifc_points_original[::100, 0], ifc_points_original[::100, 2], \n", "                  c='red', alpha=0.6, s=1, label='IFC')\n", "axes[1, 0].set_xlabel('X (UTM)')\n", "axes[1, 0].set_ylabel('Z (Elevation)')\n", "axes[1, 0].set_title('Before Correction (XZ)')\n", "axes[1, 0].legend()\n", "axes[1, 0].grid(True, alpha=0.3)\n", "\n", "# Plot 4: After correction (XZ)\n", "axes[1, 1].scatter(drone_points_corrected[::10, 0], drone_points_corrected[::10, 2], \n", "                  c='blue', alpha=0.6, s=1, label='Drone')\n", "axes[1, 1].scatter(ifc_points_corrected[::100, 0], ifc_points_corrected[::100, 2], \n", "                  c='red', alpha=0.6, s=1, label='IFC (corrected)')\n", "axes[1, 1].set_xlabel('X (UTM)')\n", "axes[1, 1].set_ylabel('Z (Elevation)')\n", "axes[1, 1].set_title('After Correction (XZ)')\n", "axes[1, 1].legend()\n", "axes[1, 1].grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.savefig(output_path / 'coordinate_correction_results.png', dpi=300, bbox_inches='tight')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Save Corrected Point Clouds"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if save_corrected_files:\n", "    print(f\"\\n💾 SAVING CORRECTED POINT CLOUDS\")\n", "    \n", "    # Save corrected drone point cloud (unchanged, but for consistency)\n", "    drone_corrected_pcd = o3d.geometry.PointCloud()\n", "    drone_corrected_pcd.points = o3d.utility.Vector3dVector(drone_points_corrected)\n", "    if drone_pcd.has_colors():\n", "        drone_corrected_pcd.colors = drone_pcd.colors\n", "    \n", "    drone_output_file = output_path / f\"{site_name}_drone_{ground_method}_corrected.ply\"\n", "    o3d.io.write_point_cloud(str(drone_output_file), drone_corrected_pcd)\n", "    print(f\"✅ Saved corrected drone: {drone_output_file}\")\n", "    \n", "    # Save corrected IFC point cloud\n", "    ifc_corrected_pcd = o3d.geometry.PointCloud()\n", "    ifc_corrected_pcd.points = o3d.utility.Vector3dVector(ifc_points_corrected)\n", "    if ifc_pcd.has_colors():\n", "        ifc_corrected_pcd.colors = ifc_pcd.colors\n", "    \n", "    ifc_output_file = output_path / f\"{site_name}_ifc_corrected.ply\"\n", "    o3d.io.write_point_cloud(str(ifc_output_file), ifc_corrected_pcd)\n", "    print(f\"✅ Saved corrected IFC: {ifc_output_file}\")\n", "    \n", "    # Save correction metadata\n", "    correction_metadata = {\n", "        'timestamp': datetime.now().isoformat(),\n", "        'site_name': site_name,\n", "        'ground_method': ground_method,\n", "        'corrections_applied': {\n", "            'x_offset': float(x_offset),\n", "            'y_offset': float(y_offset),\n", "            'z_offset': float(z_offset),\n", "            'correction_magnitude': float(np.linalg.norm([x_offset, y_offset, z_offset]))\n", "        },\n", "        'results': {\n", "            'remaining_offset_magnitude': float(np.linalg.norm(remaining_offset)),\n", "            'alignment_success': bool(alignment_success),\n", "            'drone_points': int(len(drone_points_corrected)),\n", "            'ifc_points': int(len(ifc_points_corrected))\n", "        },\n", "        'output_files': {\n", "            'drone_corrected': str(drone_output_file.name),\n", "            'ifc_corrected': str(ifc_output_file.name)\n", "        }\n", "    }\n", "    \n", "    metadata_file = output_path / 'correction_metadata.json'\n", "    with open(metadata_file, 'w') as f:\n", "        json.dump(correction_metadata, f, indent=2)\n", "    print(f\"✅ Saved metadata: {metadata_file}\")\n", "    \n", "    print(f\"\\n🎯 All corrected files saved to: {output_path}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Summary\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"🎯 COORDINATE CORRECTION SUMMARY\")\n", "print(\"=\"*60)\n", "print(f\"Site: {site_name}\")\n", "print(f\"Ground method: {ground_method}\")\n", "print(f\"\\nCorrections applied:\")\n", "print(f\"  X offset: {x_offset:.2f}m\")\n", "print(f\"  Y offset: {y_offset:.2f}m\")\n", "print(f\"  Z offset: {z_offset:.2f}m\")\n", "print(f\"\\nResults:\")\n", "print(f\"  Remaining offset: {np.linalg.norm(remaining_offset):.2f}m\")\n", "print(f\"  Alignment success: {'✅ Yes' if alignment_success else '❌ No'}\")\n", "print(f\"\\nNext steps:\")\n", "print(f\"  1. Use corrected point clouds for ICP alignment\")\n", "print(f\"  2. Expected RMSE improvement: 6.64-19.37m → <1.0m\")\n", "print(f\"  3. Proceed with feature-based alignment using pile detection\")\n", "print(\"=\"*60)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}