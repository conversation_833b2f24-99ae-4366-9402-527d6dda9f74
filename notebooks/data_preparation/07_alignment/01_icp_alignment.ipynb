# Parameters (Papermill)
ground_method = "ransac_pmf"  # Ground segmentation method: csf, pmf, ransac
site_name = "trino_enel"
icp_max_iterations = 50
icp_tolerance = 1e-6
voxel_size = 0.02  # For downsampling if needed
output_dir = "../../../data/output_runs/icp_alignment"
enable_visualization = True
save_results = True

# Imports
import numpy as np
import open3d as o3d
import matplotlib.pyplot as plt
import pandas as pd
import time
import json
from pathlib import Path
from datetime import datetime
from scipy.spatial import cKDTree

# Setup
np.random.seed(42)
output_path = Path(output_dir) / ground_method
output_path.mkdir(parents=True, exist_ok=True)

print(f"ICP ALIGNMENT - {ground_method.upper()}")
print(f"Site: {site_name}")
print(f"Output: {output_path}")
print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

# Define file paths
drone_file = Path(f"../../../data/processed/{site_name}/ground_segmentation/{ground_method}/trino_enel_nonground.ply")
ifc_file = Path(f"../../../data/processed/{site_name}/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply")

print("Loading point clouds...")
print(f"Drone scan (non-ground): {drone_file}")
print(f"Drone exists: {drone_file.exists()}")
print(f"IFC point cloud: {ifc_file}")
print(f"IFC exists: {ifc_file.exists()}")

# Load point clouds
if drone_file.exists() and ifc_file.exists():
    drone_pcd = o3d.io.read_point_cloud(str(drone_file))
    ifc_pcd = o3d.io.read_point_cloud(str(ifc_file))
    
    drone_points = np.asarray(drone_pcd.points)
    ifc_points = np.asarray(ifc_pcd.points)
    
    print(f"Loaded drone scan: {drone_points.shape[0]:,} points")
    print(f"Loaded IFC model: {ifc_points.shape[0]:,} points")
    
    # Store original for comparison
    drone_pcd_original = drone_pcd
    ifc_pcd_original = ifc_pcd
    
else:
    print("Error: Required point cloud files not found!")
    raise FileNotFoundError("Missing point cloud files")

# Display initial statistics
print("\nINITIAL POINT CLOUD STATISTICS")
print("\nDrone scan (ground truth):")
print(f"  Points: {drone_points.shape[0]:,}")
print(f"  X range: [{drone_points[:, 0].min():.2f}, {drone_points[:, 0].max():.2f}]")
print(f"  Y range: [{drone_points[:, 1].min():.2f}, {drone_points[:, 1].max():.2f}]")
print(f"  Z range: [{drone_points[:, 2].min():.2f}, {drone_points[:, 2].max():.2f}]")

print("\nIFC model (to be aligned):")
print(f"  Points: {ifc_points.shape[0]:,}")
print(f"  X range: [{ifc_points[:, 0].min():.2f}, {ifc_points[:, 0].max():.2f}]")
print(f"  Y range: [{ifc_points[:, 1].min():.2f}, {ifc_points[:, 1].max():.2f}]")
print(f"  Z range: [{ifc_points[:, 2].min():.2f}, {ifc_points[:, 2].max():.2f}]")

# Calculate coordinate system differences
drone_center = drone_points.mean(axis=0)
ifc_center = ifc_points.mean(axis=0)
center_offset = drone_center - ifc_center

print("\nCOORDINATE SYSTEM ANALYSIS")
print(f"Drone center: [{drone_center[0]:.2f}, {drone_center[1]:.2f}, {drone_center[2]:.2f}]")
print(f"IFC center:   [{ifc_center[0]:.2f}, {ifc_center[1]:.2f}, {ifc_center[2]:.2f}]")
print(f"Offset:       [{center_offset[0]:.2f}, {center_offset[1]:.2f}, {center_offset[2]:.2f}]")
print(f"\nOffset magnitude: {np.linalg.norm(center_offset):.2f} meters")

# Z-shift correction: Align Z-coordinates roughly
# Use median Z values for robust estimation
drone_z_median = np.median(drone_points[:, 2])
ifc_z_median = np.median(ifc_points[:, 2])
z_shift = drone_z_median - ifc_z_median

print(f"\nZ-SHIFT CORRECTION")
print(f"Drone Z median: {drone_z_median:.2f} m")
print(f"IFC Z median:   {ifc_z_median:.2f} m")
print(f"Z-shift needed: {z_shift:.2f} m")

# Apply Z-shift to IFC point cloud
ifc_pcd_shifted = ifc_pcd.translate([0, 0, z_shift])
ifc_points_shifted = np.asarray(ifc_pcd_shifted.points)

print(f"Applied Z-shift of {z_shift:.2f} m to IFC point cloud")

# Verify Z-shift correction
ifc_z_median_after = np.median(ifc_points_shifted[:, 2])
z_diff_after = abs(drone_z_median - ifc_z_median_after)

print(f"\nZ-SHIFT VERIFICATION")
print(f"IFC Z median after shift: {ifc_z_median_after:.2f} m")
print(f"Remaining Z difference: {z_diff_after:.2f} m")
print(f"Z-shift successful: {z_diff_after < 1.0}")

# Prepare point clouds for ICP
# Source: IFC (to be transformed)
# Target: Drone scan (ground truth)
source_pcd = o3d.geometry.PointCloud()
source_pcd.points = o3d.utility.Vector3dVector(np.asarray(ifc_pcd_shifted.points))
target_pcd = o3d.geometry.PointCloud()
target_pcd.points = o3d.utility.Vector3dVector(np.asarray(drone_pcd.points))


print("\nICP ALIGNMENT SETUP")
print(f"Source (IFC): {len(source_pcd.points):,} points")
print(f"Target (Drone): {len(target_pcd.points):,} points")
print(f"Max iterations: {icp_max_iterations}")
print(f"Tolerance: {icp_tolerance}")

# Coarse alignment: Initial rough alignment using centroids
source_center = source_pcd.get_center()
target_center = target_pcd.get_center()
coarse_translation = target_center - source_center

print(f"\nCOARSE ALIGNMENT")
print(f"Source center: [{source_center[0]:.2f}, {source_center[1]:.2f}, {source_center[2]:.2f}]")
print(f"Target center: [{target_center[0]:.2f}, {target_center[1]:.2f}, {target_center[2]:.2f}]")
print(f"Coarse translation: [{coarse_translation[0]:.2f}, {coarse_translation[1]:.2f}, {coarse_translation[2]:.2f}]")

# Apply coarse alignment
source_pcd_coarse = source_pcd.translate(coarse_translation)
print("Applied coarse alignment")

# Fine alignment: ICP registration
print(f"\nFINE ALIGNMENT (ICP)")
print("Running ICP registration...")

start_time = time.time()

# Set up ICP parameters
threshold = 0.5  # Distance threshold for correspondence
trans_init = np.eye(4)  # Identity matrix as initial transformation

# Run ICP
reg_p2p = o3d.pipelines.registration.registration_icp(
    source_pcd_coarse, target_pcd, threshold, trans_init,
    o3d.pipelines.registration.TransformationEstimationPointToPoint(),
    o3d.pipelines.registration.ICPConvergenceCriteria(
        max_iteration=icp_max_iterations,
        relative_fitness=icp_tolerance,
        relative_rmse=icp_tolerance
    )
)

icp_time = time.time() - start_time

print(f"ICP completed in {icp_time:.2f} seconds")
print(f"Fitness: {reg_p2p.fitness:.6f}")
print(f"Inlier RMSE: {reg_p2p.inlier_rmse:.6f} meters")

# Apply final transformation
source_pcd_aligned = source_pcd_coarse.transform(reg_p2p.transformation)

print(f"\nFINAL TRANSFORMATION")
print("Transformation matrix:")
print(reg_p2p.transformation)

# Extract rotation and translation components
rotation_matrix = reg_p2p.transformation[:3, :3]
translation_vector = reg_p2p.transformation[:3, 3]

print(f"\nTranslation: [{translation_vector[0]:.3f}, {translation_vector[1]:.3f}, {translation_vector[2]:.3f}] meters")

# Calculate rotation angles (approximate)
import math
rotation_x = math.atan2(rotation_matrix[2, 1], rotation_matrix[2, 2]) * 180 / math.pi
rotation_y = math.atan2(-rotation_matrix[2, 0], math.sqrt(rotation_matrix[2, 1]**2 + rotation_matrix[2, 2]**2)) * 180 / math.pi
rotation_z = math.atan2(rotation_matrix[1, 0], rotation_matrix[0, 0]) * 180 / math.pi

print(f"Rotation (approx): X={rotation_x:.2f}°, Y={rotation_y:.2f}°, Z={rotation_z:.2f}°")

if enable_visualization:
    # Create visualization
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle(f'ICP Alignment Results - {ground_method.upper()}', fontsize=16)
    
    # Get point arrays for plotting
    drone_pts = np.asarray(target_pcd.points)
    ifc_original_pts = np.asarray(ifc_pcd_original.points)
    ifc_aligned_pts = np.asarray(source_pcd_aligned.points)
    
    # Plot 1: Before alignment (XY view)
    axes[0, 0].scatter(drone_pts[:, 0], drone_pts[:, 1], c='blue', alpha=0.6, s=1, label='Drone (target)')
    axes[0, 0].scatter(ifc_original_pts[:, 0], ifc_original_pts[:, 1], c='red', alpha=0.6, s=1, label='IFC (original)')
    axes[0, 0].set_title('Before Alignment (XY view)')
    axes[0, 0].set_xlabel('X (m)')
    axes[0, 0].set_ylabel('Y (m)')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    axes[0, 0].axis('equal')
    
    # Plot 2: After alignment (XY view)
    axes[0, 1].scatter(drone_pts[:, 0], drone_pts[:, 1], c='blue', alpha=0.6, s=1, label='Drone (target)')
    axes[0, 1].scatter(ifc_aligned_pts[:, 0], ifc_aligned_pts[:, 1], c='green', alpha=0.6, s=1, label='IFC (aligned)')
    axes[0, 1].set_title('After Alignment (XY view)')
    axes[0, 1].set_xlabel('X (m)')
    axes[0, 1].set_ylabel('Y (m)')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    axes[0, 1].axis('equal')
    
    # Plot 3: Before alignment (XZ view)
    axes[1, 0].scatter(drone_pts[:, 0], drone_pts[:, 2], c='blue', alpha=0.6, s=1, label='Drone (target)')
    axes[1, 0].scatter(ifc_original_pts[:, 0], ifc_original_pts[:, 2], c='red', alpha=0.6, s=1, label='IFC (original)')
    axes[1, 0].set_title('Before Alignment (XZ view)')
    axes[1, 0].set_xlabel('X (m)')
    axes[1, 0].set_ylabel('Z (m)')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # Plot 4: After alignment (XZ view)
    axes[1, 1].scatter(drone_pts[:, 0], drone_pts[:, 2], c='blue', alpha=0.6, s=1, label='Drone (target)')
    axes[1, 1].scatter(ifc_aligned_pts[:, 0], ifc_aligned_pts[:, 2], c='green', alpha=0.6, s=1, label='IFC (aligned)')
    axes[1, 1].set_title('After Alignment (XZ view)')
    axes[1, 1].set_xlabel('X (m)')
    axes[1, 1].set_ylabel('Z (m)')
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    if save_results:
        plt.savefig(output_path / f'icp_alignment_visualization_{ground_method}.png', dpi=300, bbox_inches='tight')
        print(f"Saved visualization to: {output_path / f'icp_alignment_visualization_{ground_method}.png'}")
    
    plt.show()

# Calculate alignment errors
print("\nALIGNMENT ERROR ANALYSIS")

# Get aligned points
ifc_aligned_points = np.asarray(source_pcd_aligned.points)
drone_points = np.asarray(target_pcd.points)

# Find nearest neighbors for error calculation
print("Finding nearest neighbors...")
tree = cKDTree(drone_points)
distances, indices = tree.query(ifc_aligned_points, k=1)

# Calculate error metrics
rmse = np.sqrt(np.mean(distances**2))
max_deviation = np.max(distances)
mean_deviation = np.mean(distances)
std_deviation = np.std(distances)
median_deviation = np.median(distances)

print(f"\nERROR METRICS")
print(f"RMSE: {rmse:.4f} meters")
print(f"Max deviation: {max_deviation:.4f} meters")
print(f"Mean deviation: {mean_deviation:.4f} meters")
print(f"Std deviation: {std_deviation:.4f} meters")
print(f"Median deviation: {median_deviation:.4f} meters")

# Calculate percentiles
p95 = np.percentile(distances, 95)
p99 = np.percentile(distances, 99)
print(f"95th percentile: {p95:.4f} meters")
print(f"99th percentile: {p99:.4f} meters")

# Error distribution visualization
if enable_visualization:
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))
    
    # Histogram of distances
    ax1.hist(distances, bins=50, alpha=0.7, edgecolor='black')
    ax1.axvline(rmse, color='red', linestyle='--', label=f'RMSE: {rmse:.4f}m')
    ax1.axvline(mean_deviation, color='green', linestyle='--', label=f'Mean: {mean_deviation:.4f}m')
    ax1.axvline(median_deviation, color='blue', linestyle='--', label=f'Median: {median_deviation:.4f}m')
    ax1.set_xlabel('Distance (meters)')
    ax1.set_ylabel('Frequency')
    ax1.set_title('Alignment Error Distribution')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # Cumulative distribution
    sorted_distances = np.sort(distances)
    cumulative = np.arange(1, len(sorted_distances) + 1) / len(sorted_distances) * 100
    ax2.plot(sorted_distances, cumulative)
    ax2.axvline(rmse, color='red', linestyle='--', label=f'RMSE: {rmse:.4f}m')
    ax2.axhline(95, color='orange', linestyle='--', alpha=0.7)
    ax2.axvline(p95, color='orange', linestyle='--', label=f'95%: {p95:.4f}m')
    ax2.set_xlabel('Distance (meters)')
    ax2.set_ylabel('Cumulative Percentage')
    ax2.set_title('Cumulative Error Distribution')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    if save_results:
        plt.savefig(output_path / f'icp_error_analysis_{ground_method}.png', dpi=300, bbox_inches='tight')
        print(f"Saved error analysis to: {output_path / f'icp_error_analysis_{ground_method}.png'}")
    
    plt.show()

# Save results
if save_results:
    print("\nSAVING RESULTS")
    
    # Save aligned point cloud
    aligned_file = output_path / f'icp_aligned_{ground_method}.ply'
    o3d.io.write_point_cloud(str(aligned_file), source_pcd_aligned)
    print(f"Saved aligned point cloud: {aligned_file}")
    
    # Save transformation matrix
    transform_file = output_path / f'icp_transformation_{ground_method}.npy'
    np.save(transform_file, reg_p2p.transformation)
    print(f"Saved transformation matrix: {transform_file}")
    
    # Save metrics
    metrics = {
        'timestamp': datetime.now().isoformat(),
        'ground_method': ground_method,
        'site_name': site_name,
        'icp_parameters': {
            'max_iterations': icp_max_iterations,
            'tolerance': icp_tolerance,
            'voxel_size': voxel_size
        },
        'preprocessing': {
            'z_shift_applied': float(z_shift),
            'coarse_translation': coarse_translation.tolist()
        },
        'icp_results': {
            'fitness': float(reg_p2p.fitness),
            'inlier_rmse': float(reg_p2p.inlier_rmse),
            'convergence_time_seconds': float(icp_time),
            'transformation_matrix': reg_p2p.transformation.tolist()
        },
        'error_metrics': {
            'rmse_meters': float(rmse),
            'max_deviation_meters': float(max_deviation),
            'mean_deviation_meters': float(mean_deviation),
            'std_deviation_meters': float(std_deviation),
            'median_deviation_meters': float(median_deviation),
            'p95_deviation_meters': float(p95),
            'p99_deviation_meters': float(p99)
        },
        'point_counts': {
            'drone_points': int(len(drone_points)),
            'ifc_points': int(len(ifc_aligned_points))
        }
    }
    
    metrics_file = output_path / f'icp_metrics_{ground_method}.json'
    with open(metrics_file, 'w') as f:
        json.dump(metrics, f, indent=2)
    print(f"Saved metrics: {metrics_file}")
    
    print(f"\nAll results saved to: {output_path}")

# Summary
print("\n" + "="*60)
print("ICP ALIGNMENT SUMMARY")
print("="*60)
print(f"Site: {site_name}")
print(f"Ground method: {ground_method}")
print(f"Processing time: {icp_time:.2f} seconds")
print(f"\nAlignment Quality:")
print(f"  RMSE: {rmse:.4f} meters")
print(f"  Max deviation: {max_deviation:.4f} meters")
print(f"  ICP fitness: {reg_p2p.fitness:.6f}")
print(f"  ICP inlier RMSE: {reg_p2p.inlier_rmse:.6f} meters")
print(f"\nAlignment Success: {'GOOD' if rmse < 0.5 else 'NEEDS REVIEW' if rmse < 1.0 else 'POOR'}")
print("="*60)