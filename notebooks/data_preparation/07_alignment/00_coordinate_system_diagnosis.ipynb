# Parameters
site_name = "trino_enel"
ground_method = "csf"
output_dir = "../../../data/output_runs/coordinate_diagnosis"

# Imports
import numpy as np
import pandas as pd
import open3d as o3d
import matplotlib.pyplot as plt
from pathlib import Path
import json
from datetime import datetime

# Setup
output_path = Path(output_dir)
output_path.mkdir(parents=True, exist_ok=True)

print(f"🔍 COORDINATE SYSTEM DIAGNOSIS")
print(f"Site: {site_name}")
print(f"Ground method: {ground_method}")
print(f"Output: {output_path}")

# Load drone point cloud
drone_file = Path(f"../../../data/processed/{site_name}/ground_segmentation/{ground_method}/trino_enel_nonground.ply")
if not drone_file.exists():
    raise FileNotFoundError(f"Drone file not found: {drone_file}")

drone_pcd = o3d.io.read_point_cloud(str(drone_file))
drone_points = np.asarray(drone_pcd.points)

print(f"✅ Loaded drone point cloud: {drone_points.shape[0]:,} points")
print(f"Drone bounds:")
print(f"  X: [{drone_points[:, 0].min():.2f}, {drone_points[:, 0].max():.2f}]")
print(f"  Y: [{drone_points[:, 1].min():.2f}, {drone_points[:, 1].max():.2f}]")
print(f"  Z: [{drone_points[:, 2].min():.2f}, {drone_points[:, 2].max():.2f}]")

# Load IFC point cloud
ifc_file = Path(f"../../../data/processed/{site_name}/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply")
if not ifc_file.exists():
    raise FileNotFoundError(f"IFC file not found: {ifc_file}")

ifc_pcd = o3d.io.read_point_cloud(str(ifc_file))
ifc_points = np.asarray(ifc_pcd.points)

print(f"\n✅ Loaded IFC point cloud: {ifc_points.shape[0]:,} points")
print(f"IFC bounds:")
print(f"  X: [{ifc_points[:, 0].min():.2f}, {ifc_points[:, 0].max():.2f}]")
print(f"  Y: [{ifc_points[:, 1].min():.2f}, {ifc_points[:, 1].max():.2f}]")
print(f"  Z: [{ifc_points[:, 2].min():.2f}, {ifc_points[:, 2].max():.2f}]")

# Load IFC metadata for pile coordinates
ifc_metadata_file = Path(f"../../../data/processed/{site_name}/ifc_metadata/GRE.EEC.S.00.IT.P.14353.00.265_elements_detailed.csv")
if ifc_metadata_file.exists():
    ifc_metadata = pd.read_csv(ifc_metadata_file)
    
    # Filter for piles (columns with pile-like names)
    pile_mask = ifc_metadata['Name'].str.contains('TRPL|Pile|pile', case=False, na=False)
    pile_metadata = ifc_metadata[pile_mask & ifc_metadata[['X', 'Y', 'Z']].notna().all(axis=1)]
    
    print(f"\n✅ Loaded IFC metadata: {len(ifc_metadata)} elements")
    print(f"Pile elements found: {len(pile_metadata)}")
    
    if len(pile_metadata) > 0:
        print(f"Pile coordinate bounds:")
        print(f"  X: [{pile_metadata['X'].min():.2f}, {pile_metadata['X'].max():.2f}]")
        print(f"  Y: [{pile_metadata['Y'].min():.2f}, {pile_metadata['Y'].max():.2f}]")
        print(f"  Z: [{pile_metadata['Z'].min():.2f}, {pile_metadata['Z'].max():.2f}]")
else:
    print("\n⚠️ IFC metadata file not found")
    pile_metadata = pd.DataFrame()

# Calculate centroids and offsets
drone_centroid = drone_points.mean(axis=0)
ifc_centroid = ifc_points.mean(axis=0)
centroid_offset = drone_centroid - ifc_centroid

print(f"📊 COORDINATE SYSTEM ANALYSIS")
print(f"\nCentroids:")
print(f"  Drone: [{drone_centroid[0]:.2f}, {drone_centroid[1]:.2f}, {drone_centroid[2]:.2f}]")
print(f"  IFC:   [{ifc_centroid[0]:.2f}, {ifc_centroid[1]:.2f}, {ifc_centroid[2]:.2f}]")
print(f"  Offset: [{centroid_offset[0]:.2f}, {centroid_offset[1]:.2f}, {centroid_offset[2]:.2f}]")
print(f"  Offset magnitude: {np.linalg.norm(centroid_offset):.2f} meters")

# Analyze coordinate ranges
drone_ranges = {
    'x_range': drone_points[:, 0].max() - drone_points[:, 0].min(),
    'y_range': drone_points[:, 1].max() - drone_points[:, 1].min(),
    'z_range': drone_points[:, 2].max() - drone_points[:, 2].min()
}

ifc_ranges = {
    'x_range': ifc_points[:, 0].max() - ifc_points[:, 0].min(),
    'y_range': ifc_points[:, 1].max() - ifc_points[:, 1].min(),
    'z_range': ifc_points[:, 2].max() - ifc_points[:, 2].min()
}

print(f"\nSpatial extents:")
print(f"  Drone: X={drone_ranges['x_range']:.1f}m, Y={drone_ranges['y_range']:.1f}m, Z={drone_ranges['z_range']:.1f}m")
print(f"  IFC:   X={ifc_ranges['x_range']:.1f}m, Y={ifc_ranges['y_range']:.1f}m, Z={ifc_ranges['z_range']:.1f}m")

# Check coordinate system compatibility
print(f"\n🔍 COORDINATE SYSTEM COMPATIBILITY")

# Check if coordinates are in similar ranges (UTM-like)
drone_utm_like = (drone_points[:, 0].min() > 100000 and drone_points[:, 1].min() > 1000000)
ifc_utm_like = (ifc_points[:, 0].min() > 100000 and ifc_points[:, 1].min() > 1000000)

print(f"UTM-like coordinates:")
print(f"  Drone: {'✅ Yes' if drone_utm_like else '❌ No'}")
print(f"  IFC:   {'✅ Yes' if ifc_utm_like else '❌ No'}")

# Check overlap in coordinate ranges
x_overlap = not (drone_points[:, 0].max() < ifc_points[:, 0].min() or 
                ifc_points[:, 0].max() < drone_points[:, 0].min())
y_overlap = not (drone_points[:, 1].max() < ifc_points[:, 1].min() or 
                ifc_points[:, 1].max() < drone_points[:, 1].min())

print(f"\nCoordinate range overlap:")
print(f"  X-axis: {'✅ Yes' if x_overlap else '❌ No'}")
print(f"  Y-axis: {'✅ Yes' if y_overlap else '❌ No'}")

# Calculate relative offset as percentage of site size
site_size = max(drone_ranges['x_range'], drone_ranges['y_range'])
relative_offset = np.linalg.norm(centroid_offset[:2]) / site_size * 100

print(f"\nOffset analysis:")
print(f"  Site size: {site_size:.1f}m")
print(f"  Horizontal offset: {np.linalg.norm(centroid_offset[:2]):.2f}m")
print(f"  Relative offset: {relative_offset:.1f}% of site size")
print(f"  Z offset: {abs(centroid_offset[2]):.2f}m")

# Create visualization
fig, axes = plt.subplots(2, 2, figsize=(15, 12))
fig.suptitle('Coordinate System Diagnosis', fontsize=16)

# Plot 1: XY overview
axes[0, 0].scatter(drone_points[::10, 0], drone_points[::10, 1], 
                  c='blue', alpha=0.6, s=1, label='Drone')
axes[0, 0].scatter(ifc_points[::10, 0], ifc_points[::10, 1], 
                  c='red', alpha=0.6, s=1, label='IFC')
axes[0, 0].set_xlabel('X (UTM Easting)')
axes[0, 0].set_ylabel('Y (UTM Northing)')
axes[0, 0].set_title('XY Overview')
axes[0, 0].legend()
axes[0, 0].grid(True, alpha=0.3)
axes[0, 0].axis('equal')

# Plot 2: XZ side view
axes[0, 1].scatter(drone_points[::10, 0], drone_points[::10, 2], 
                  c='blue', alpha=0.6, s=1, label='Drone')
axes[0, 1].scatter(ifc_points[::10, 0], ifc_points[::10, 2], 
                  c='red', alpha=0.6, s=1, label='IFC')
axes[0, 1].set_xlabel('X (UTM Easting)')
axes[0, 1].set_ylabel('Z (Elevation)')
axes[0, 1].set_title('XZ Side View')
axes[0, 1].legend()
axes[0, 1].grid(True, alpha=0.3)

# Plot 3: Centered comparison (remove mean offset)
drone_centered = drone_points - drone_centroid
ifc_centered = ifc_points - ifc_centroid

axes[1, 0].scatter(drone_centered[::10, 0], drone_centered[::10, 1], 
                  c='blue', alpha=0.6, s=1, label='Drone (centered)')
axes[1, 0].scatter(ifc_centered[::10, 0], ifc_centered[::10, 1], 
                  c='red', alpha=0.6, s=1, label='IFC (centered)')
axes[1, 0].set_xlabel('X (relative to centroid)')
axes[1, 0].set_ylabel('Y (relative to centroid)')
axes[1, 0].set_title('Centered Comparison')
axes[1, 0].legend()
axes[1, 0].grid(True, alpha=0.3)
axes[1, 0].axis('equal')

# Plot 4: Pile locations (if available)
if len(pile_metadata) > 0:
    axes[1, 1].scatter(drone_points[::50, 0], drone_points[::50, 1], 
                      c='lightblue', alpha=0.3, s=1, label='Drone')
    axes[1, 1].scatter(pile_metadata['X'], pile_metadata['Y'], 
                      c='red', s=50, marker='x', linewidth=2, label='IFC Piles')
    axes[1, 1].set_xlabel('X (UTM Easting)')
    axes[1, 1].set_ylabel('Y (UTM Northing)')
    axes[1, 1].set_title(f'Pile Locations ({len(pile_metadata)} piles)')
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)
else:
    axes[1, 1].text(0.5, 0.5, 'No pile metadata\navailable', 
                   ha='center', va='center', transform=axes[1, 1].transAxes)
    axes[1, 1].set_title('Pile Locations')

plt.tight_layout()
plt.savefig(output_path / 'coordinate_system_diagnosis.png', dpi=300, bbox_inches='tight')
plt.show()

# Generate diagnosis summary
diagnosis = {
    'timestamp': datetime.now().isoformat(),
    'site_name': site_name,
    'ground_method': ground_method,
    
    'coordinate_systems': {
        'drone_utm_like': bool(drone_utm_like),
        'ifc_utm_like': bool(ifc_utm_like),
        'systems_compatible': bool(drone_utm_like and ifc_utm_like)
    },
    
    'spatial_overlap': {
        'x_overlap': bool(x_overlap),
        'y_overlap': bool(y_overlap),
        'spatial_compatibility': bool(x_overlap and y_overlap)
    },
    
    'offsets': {
        'centroid_offset_x': float(centroid_offset[0]),
        'centroid_offset_y': float(centroid_offset[1]),
        'centroid_offset_z': float(centroid_offset[2]),
        'horizontal_offset_magnitude': float(np.linalg.norm(centroid_offset[:2])),
        'total_offset_magnitude': float(np.linalg.norm(centroid_offset)),
        'relative_offset_percent': float(relative_offset)
    },
    
    'dataset_info': {
        'drone_points': int(len(drone_points)),
        'ifc_points': int(len(ifc_points)),
        'pile_elements': int(len(pile_metadata)) if len(pile_metadata) > 0 else 0
    },
    
    'bounds': {
        'drone': {
            'x_min': float(drone_points[:, 0].min()),
            'x_max': float(drone_points[:, 0].max()),
            'y_min': float(drone_points[:, 1].min()),
            'y_max': float(drone_points[:, 1].max()),
            'z_min': float(drone_points[:, 2].min()),
            'z_max': float(drone_points[:, 2].max())
        },
        'ifc': {
            'x_min': float(ifc_points[:, 0].min()),
            'x_max': float(ifc_points[:, 0].max()),
            'y_min': float(ifc_points[:, 1].min()),
            'y_max': float(ifc_points[:, 1].max()),
            'z_min': float(ifc_points[:, 2].min()),
            'z_max': float(ifc_points[:, 2].max())
        }
    }
}

# Save diagnosis
diagnosis_file = output_path / 'coordinate_diagnosis.json'
with open(diagnosis_file, 'w') as f:
    json.dump(diagnosis, f, indent=2)

print(f"\n💾 Saved diagnosis: {diagnosis_file}")

# Print diagnosis summary
print("\n" + "="*60)
print("🎯 COORDINATE SYSTEM DIAGNOSIS SUMMARY")
print("="*60)

print(f"\n📍 COORDINATE SYSTEMS:")
print(f"  Both datasets: {'✅ UTM-compatible' if drone_utm_like and ifc_utm_like else '❌ Incompatible'}")
print(f"  Spatial overlap: {'✅ Yes' if x_overlap and y_overlap else '❌ No'}")

print(f"\n📏 OFFSETS:")
print(f"  Horizontal: {np.linalg.norm(centroid_offset[:2]):.2f}m ({relative_offset:.1f}% of site)")
print(f"  Vertical: {abs(centroid_offset[2]):.2f}m")
print(f"  Total: {np.linalg.norm(centroid_offset):.2f}m")

print(f"\n🔧 RECOMMENDED ACTIONS:")
if drone_utm_like and ifc_utm_like and x_overlap and y_overlap:
    if np.linalg.norm(centroid_offset) < 100:
        print(f"  ✅ Coordinate systems are compatible!")
        print(f"  ✅ Small offset ({np.linalg.norm(centroid_offset):.1f}m) can be corrected")
        print(f"  🎯 Proceed with simple translation correction")
        print(f"  🎯 Use feature-based alignment for fine-tuning")
    else:
        print(f"  ⚠️ Large offset ({np.linalg.norm(centroid_offset):.1f}m) needs investigation")
        print(f"  🔧 Check for systematic coordinate transformation issues")
else:
    print(f"  ❌ Coordinate system incompatibility detected")
    print(f"  🔧 Need proper coordinate reference system transformation")

print(f"\n📊 DATASET INFO:")
print(f"  Drone points: {len(drone_points):,}")
print(f"  IFC points: {len(ifc_points):,}")
print(f"  Pile elements: {len(pile_metadata) if len(pile_metadata) > 0 else 0}")

print("="*60)