import json
import numpy as np
from pathlib import Path
import pandas as pd
import matplotlib.pyplot as plt


results_dir = Path("../../../data/output_runs/icp_alignment")
methods = ["csf", "pmf", "ransac", "ransac_pmf"]



all_results = []

for method in methods:
    metrics_path = results_dir / method / f"icp_metrics_{method}.json"
    transform_path = results_dir / method / f"icp_transformation_{method}.npy"

    if metrics_path.exists() and transform_path.exists():
        with open(metrics_path) as f:
            data = json.load(f)

        transform = np.load(transform_path)

        # Flatten and map correct keys
        row = {
            "method": method,
            "rmse": data["error_metrics"]["rmse_meters"],
            "max_deviation": data["error_metrics"]["max_deviation_meters"],
            "mean_deviation": data["error_metrics"]["mean_deviation_meters"],
            "median_deviation": data["error_metrics"]["median_deviation_meters"],
            "std_deviation": data["error_metrics"]["std_deviation_meters"],
            "fitness": data["icp_results"]["fitness"],
            "inlier_rmse": data["icp_results"]["inlier_rmse"],
            "transformation": transform.tolist()
        }

        all_results.append(row)
    else:
        print(f"Missing data for method: {method}")

df = pd.DataFrame(all_results)

df = df[[
    "method", "rmse", "max_deviation", "mean_deviation",
    "median_deviation", "std_deviation", "fitness", "inlier_rmse"
]]

# Display summary
df.sort_values("rmse").style.background_gradient(subset=["rmse", "fitness"], cmap="RdYlGn_r")


# Explanation of alignment performance
for idx, row in df.iterrows():
    print(f"\nMethod: {row['method'].upper()}")
    print(f"  RMSE: {row['rmse']:.2f} m — {'Acceptable' if row['rmse'] < 1 else 'High Error'}")
    print(f"  ICP Fitness: {row['fitness']:.6f} — {'Good' if row['fitness'] > 0.5 else 'Poor match'}")
    print(f"  Max deviation: {row['max_deviation']:.2f} m")
    print(f"  Inlier RMSE: {row['inlier_rmse']:.3f} m")


# Compare RMSE and Fitness
fig, axes = plt.subplots(1, 2, figsize=(12, 4))
df.plot(x="method", y="rmse", kind="bar", ax=axes[0], title="RMSE per Method", legend=False)
df.plot(x="method", y="fitness", kind="bar", ax=axes[1], title="ICP Fitness per Method", legend=False)
plt.tight_layout()
plt.show()


# View a specific method's transformation matrix
method_to_inspect = "pmf"
transform = np.load(results_dir / method_to_inspect / f"icp_transformation_{method_to_inspect}.npy")
print(f"Transformation matrix for '{method_to_inspect}':\n")
print(np.round(transform, 3))




