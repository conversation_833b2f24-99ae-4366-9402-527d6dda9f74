{
 "cells": [
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# ICP Point Cloud Alignment with Coordinate Correction\n",
    "\n",
    "This notebook implements ICP alignment using coordinate-corrected point clouds to resolve the alignment issues identified in the original ICP notebook.\n",
    "\n",
    "**Key Improvements:**\n",
    "- Uses coordinate-corrected point clouds (if available)\n",
    "- Resolves 74.3m horizontal and 154.9m vertical offsets\n",
    "- Expected RMSE improvement: 6.64-19.37m → <1.0m\n",
    "- Maintains compatibility with original files as fallback\n",
    "\n",
    "**Comparison with Original:**\n",
    "- `01_icp_alignment.ipynb`: Shows original issues (RMSE 6.64-19.37m)\n",
    "- `02_icp_alignment_with_correction.ipynb`: This notebook with fixes\n",
    "\n",
    "**Author**: Preetam Balijepalli  \n",
    "**Date**: July 2025"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {
    "tags": [
     "parameters"
    ]
   },
   "outputs": [],
   "source": [
    "# Parameters (Papermill)\n",
    "ground_method = \"csf\"  # Ground segmentation method: csf, pmf, ransac\n",
    "site_name = \"trino_enel\"\n",
    "icp_max_iterations = 50\n",
    "icp_tolerance = 1e-6\n",
    "voxel_size = 0.02  # For downsampling if needed\n",
    "output_dir = \"../../data/output_runs/icp_alignment_corrected\"\n",
    "enable_visualization = True\n",
    "save_results = True\n",
    "use_coordinate_correction = True  # Enable coordinate correction"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Imports\n",
    "import numpy as np\n",
    "import open3d as o3d\n",
    "import matplotlib.pyplot as plt\n",
    "import pandas as pd\n",
    "import time\n",
    "import json\n",
    "from pathlib import Path\n",
    "from datetime import datetime\n",
    "from scipy.spatial import cKDTree\n",
    "\n",
    "# Setup\n",
    "np.random.seed(42)\n",
    "output_path = Path(output_dir) / ground_method\n",
    "output_path.mkdir(parents=True, exist_ok=True)\n",
    "\n",
    "print(f\"🔧 ICP ALIGNMENT WITH COORDINATE CORRECTION - {ground_method.upper()}\")\n",
    "print(f\"Site: {site_name}\")\n",
    "print(f\"Output: {output_path}\")\n",
    "print(f\"Coordinate correction: {'✅ Enabled' if use_coordinate_correction else '❌ Disabled'}\")\n",
    "print(f\"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 1. Load Data with Coordinate Correction Support"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Define file paths - Use corrected coordinates if available\n",
    "if use_coordinate_correction:\n",
    "    corrected_drone_file = Path(f\"../../data/processed/{site_name}/aligned_coordinates/{site_name}_drone_{ground_method}_corrected.ply\")\n",
    "    corrected_ifc_file = Path(f\"../../data/processed/{site_name}/aligned_coordinates/{site_name}_ifc_corrected.ply\")\n",
    "    \n",
    "    # Use corrected files if they exist, otherwise fall back to original\n",
    "    if corrected_drone_file.exists() and corrected_ifc_file.exists():\n",
    "        drone_file = corrected_drone_file\n",
    "        ifc_file = corrected_ifc_file\n",
    "        coordinate_status = \"✅ Using coordinate-corrected point clouds\"\n",
    "        print(coordinate_status)\n",
    "        print(f\"  Drone (corrected): {drone_file}\")\n",
    "        print(f\"  IFC (corrected): {ifc_file}\")\n",
    "    else:\n",
    "        drone_file = Path(f\"../../data/processed/{site_name}/ground_segmentation/{ground_method}/trino_enel_nonground.ply\")\n",
    "        ifc_file = Path(f\"../../data/processed/{site_name}/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\")\n",
    "        coordinate_status = \"⚠️ Corrected files not found - using original point clouds\"\n",
    "        print(coordinate_status)\n",
    "        print(f\"  💡 Run 01_coordinate_correction.ipynb first for better results\")\n",
    "        print(f\"  Drone (original): {drone_file}\")\n",
    "        print(f\"  IFC (original): {ifc_file}\")\nelse:\n",
    "    # Use original files\n",
    "    drone_file = Path(f\"../../data/processed/{site_name}/ground_segmentation/{ground_method}/trino_enel_nonground.ply\")\n",
    "    ifc_file = Path(f\"../../data/processed/{site_name}/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\")\n",
    "    coordinate_status = \"📋 Using original point clouds (coordinate correction disabled)\"\n",
    "    print(coordinate_status)\n",
    "\n",
    "print(f\"\\n📁 Loading point clouds...\")\n",
    "print(f\"Drone exists: {drone_file.exists()}\")\n",
    "print(f\"IFC exists: {ifc_file.exists()}\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Load point clouds\n",
    "if drone_file.exists() and ifc_file.exists():\n",
    "    drone_pcd = o3d.io.read_point_cloud(str(drone_file))\n",
    "    ifc_pcd = o3d.io.read_point_cloud(str(ifc_file))\n",
    "    \n",
    "    drone_points = np.asarray(drone_pcd.points)\n",
    "    ifc_points = np.asarray(ifc_pcd.points)\n",
    "    \n",
    "    print(f\"✅ Loaded drone scan: {drone_points.shape[0]:,} points\")\n",
    "    print(f\"✅ Loaded IFC model: {ifc_points.shape[0]:,} points\")\n",
    "    \n",
    "    # Store original for comparison\n",
    "    drone_pcd_original = drone_pcd\n",
    "    ifc_pcd_original = ifc_pcd\n",
    "    \n",
    "else:\n",
    "    print(\"❌ Error: Required point cloud files not found!\")\n",
    "    if not drone_file.exists():\n",
    "        print(f\"  Missing: {drone_file}\")\n",
    "    if not ifc_file.exists():\n",
    "        print(f\"  Missing: {ifc_file}\")\n",
    "    raise FileNotFoundError(\"Missing point cloud files\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Display initial statistics and coordinate analysis\n",
    "print(\"\\n📊 POINT CLOUD STATISTICS\")\n",
    "print(\"\\nDrone scan (ground truth):\")\n",
    "print(f\"  Points: {drone_points.shape[0]:,}\")\n",
    "print(f\"  X range: [{drone_points[:, 0].min():.2f}, {drone_points[:, 0].max():.2f}]\")\n",
    "print(f\"  Y range: [{drone_points[:, 1].min():.2f}, {drone_points[:, 1].max():.2f}]\")\n",
    "print(f\"  Z range: [{drone_points[:, 2].min():.2f}, {drone_points[:, 2].max():.2f}]\")\n",
    "\n",
    "print(\"\\nIFC model (to be aligned):\")\n",
    "print(f\"  Points: {ifc_points.shape[0]:,}\")\n",
    "print(f\"  X range: [{ifc_points[:, 0].min():.2f}, {ifc_points[:, 0].max():.2f}]\")\n",
    "print(f\"  Y range: [{ifc_points[:, 1].min():.2f}, {ifc_points[:, 1].max():.2f}]\")\n",
    "print(f\"  Z range: [{ifc_points[:, 2].min():.2f}, {ifc_points[:, 2].max():.2f}]\")\n",
    "\n",
    "# Calculate initial offset\n",
    "drone_center = drone_points.mean(axis=0)\n",
    "ifc_center = ifc_points.mean(axis=0)\n",
    "initial_offset = drone_center - ifc_center\n",
    "\n",
    "print(f\"\\n🔍 INITIAL COORDINATE ANALYSIS\")\n",
    "print(f\"Drone center: [{drone_center[0]:.2f}, {drone_center[1]:.2f}, {drone_center[2]:.2f}]\")\n",
    "print(f\"IFC center:   [{ifc_center[0]:.2f}, {ifc_center[1]:.2f}, {ifc_center[2]:.2f}]\")\n",
    "print(f\"Initial offset: [{initial_offset[0]:.2f}, {initial_offset[1]:.2f}, {initial_offset[2]:.2f}]\")\n",
    "print(f\"Offset magnitude: {np.linalg.norm(initial_offset):.2f} meters\")\n",
    "\n",
    "# Assess coordinate compatibility\n",
    "offset_magnitude = np.linalg.norm(initial_offset)\n",
    "if offset_magnitude < 10:\n",
    "    print(f\"✅ Good coordinate alignment (offset: {offset_magnitude:.2f}m)\")\nelif offset_magnitude < 100:\n",
    "    print(f\"⚠️ Moderate coordinate offset (offset: {offset_magnitude:.2f}m)\")\nelse:\n",
    "    print(f\"❌ Large coordinate offset (offset: {offset_magnitude:.2f}m)\")\n",
    "    print(f\"   Consider running coordinate correction first\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 2. Preprocess (Z-shift correction if needed)"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Z-shift correction: Only apply if using original coordinates\n",
    "if \"corrected\" not in str(drone_file) and \"corrected\" not in str(ifc_file):\n",
    "    print(f\"\\n⚡ APPLYING Z-SHIFT CORRECTION (Original coordinates detected)\")\n",
    "    \n",
    "    # Use median Z values for robust estimation\n",
    "    drone_z_median = np.median(drone_points[:, 2])\n",
    "    ifc_z_median = np.median(ifc_points[:, 2])\n",
    "    z_shift = drone_z_median - ifc_z_median\n",
    "    \n",
    "    print(f\"Drone Z median: {drone_z_median:.2f} m\")\n",
    "    print(f\"IFC Z median:   {ifc_z_median:.2f} m\")\n",
    "    print(f\"Z-shift needed: {z_shift:.2f} m\")\n",
    "    \n",
    "    # Apply Z-shift to IFC point cloud\n",
    "    ifc_pcd_shifted = ifc_pcd.translate([0, 0, z_shift])\n",
    "    ifc_points_shifted = np.asarray(ifc_pcd_shifted.points)\n",
    "    \n",
    "    print(f\"✅ Applied Z-shift of {z_shift:.2f} m to IFC point cloud\")\n",
    "    \n",
    "    # Verify Z-shift correction\n",
    "    ifc_z_median_after = np.median(ifc_points_shifted[:, 2])\n",
    "    z_diff_after = abs(drone_z_median - ifc_z_median_after)\n",
    "    \n",
    "    print(f\"\\n✓ Z-SHIFT VERIFICATION\")\n",
    "    print(f\"IFC Z median after shift: {ifc_z_median_after:.2f} m\")\n",
    "    print(f\"Remaining Z difference: {z_diff_after:.2f} m\")\n",
    "    print(f\"Z-shift successful: {z_diff_after < 1.0}\")\n",
    "    \n",
    "    # Use shifted IFC for alignment\n",
    "    ifc_pcd_for_alignment = ifc_pcd_shifted\n",
    "else:\n",
    "    print(f\"\\n✅ COORDINATE CORRECTION DETECTED\")\n",
    "    print(f\"Skipping Z-shift correction (already applied in coordinate correction)\")\n",
    "    ifc_pcd_for_alignment = ifc_pcd\n",
    "    z_shift = 0.0"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 3. Run ICP (Coarse-to-fine alignment)"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Prepare point clouds for ICP\n",
    "# Source: IFC (to be transformed)\n",
    "# Target: Drone scan (ground truth)\n",
    "source_pcd = ifc_pcd_for_alignment.copy()\n",
    "target_pcd = drone_pcd.copy()\n",
    "\n",
    "print(\"\\n🎯 ICP ALIGNMENT SETUP\")\n",
    "print(f\"Source (IFC): {len(source_pcd.points):,} points\")\n",
    "print(f\"Target (Drone): {len(target_pcd.points):,} points\")\n",
    "print(f\"Max iterations: {icp_max_iterations}\")\n",
    "print(f\"Tolerance: {icp_tolerance}\")\n",
    "print(f\"Coordinate correction applied: {'Yes' if 'corrected' in str(drone_file) else 'No'}\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Coarse alignment: Initial rough alignment using centroids\n",
    "source_center = source_pcd.get_center()\n",
    "target_center = target_pcd.get_center()\n",
    "coarse_translation = target_center - source_center\n",
    "\n",
    "print(f\"\\n🔧 COARSE ALIGNMENT\")\n",
    "print(f\"Source center: [{source_center[0]:.2f}, {source_center[1]:.2f}, {source_center[2]:.2f}]\")\n",
    "print(f\"Target center: [{target_center[0]:.2f}, {target_center[1]:.2f}, {target_center[2]:.2f}]\")\n",
    "print(f\"Coarse translation: [{coarse_translation[0]:.2f}, {coarse_translation[1]:.2f}, {coarse_translation[2]:.2f}]\")\n",
    "print(f\"Coarse offset magnitude: {np.linalg.norm(coarse_translation):.2f} meters\")\n",
    "\n",
    "# Apply coarse alignment\n",
    "source_pcd_coarse = source_pcd.translate(coarse_translation)\n",
    "print(\"✅ Applied coarse alignment\")\n",
    "\n",
    "# Verify coarse alignment\n",
    "coarse_center_after = source_pcd_coarse.get_center()\n",
    "remaining_coarse_offset = target_center - coarse_center_after\n",
    "print(f\"Remaining offset after coarse: {np.linalg.norm(remaining_coarse_offset):.4f} meters\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Fine alignment: ICP registration\n",
    "print(f\"\\n⚙️ FINE ALIGNMENT (ICP)\")\n",
    "print(\"Running ICP registration...\")\n",
    "\n",
    "start_time = time.time()\n",
    "\n",
    "# Set up ICP parameters\n",
    "threshold = 0.5  # Distance threshold for correspondence\n",
    "trans_init = np.eye(4)  # Identity matrix as initial transformation\n",
    "\n",
    "# Run ICP\n",
    "reg_p2p = o3d.pipelines.registration.registration_icp(\n",
    "    source_pcd_coarse, target_pcd, threshold, trans_init,\n",
    "    o3d.pipelines.registration.TransformationEstimationPointToPoint(),\n",
    "    o3d.pipelines.registration.ICPConvergenceCriteria(\n",
    "        max_iteration=icp_max_iterations,\n",
    "        relative_fitness=icp_tolerance,\n",
    "        relative_rmse=icp_tolerance\n",
    "    )\n",
    ")\n",
    "\n",
    "icp_time = time.time() - start_time\n",
    "\n",
    "print(f\"✅ ICP completed in {icp_time:.2f} seconds\")\n",
    "print(f\"Fitness: {reg_p2p.fitness:.6f}\")\n",
    "print(f\"Inlier RMSE: {reg_p2p.inlier_rmse:.6f} meters\")\n",
    "\n",
    "# Assess ICP quality\n",
    "if reg_p2p.fitness > 0.1:\n",
    "    print(\"✅ Good ICP convergence (fitness > 0.1)\")\nelif reg_p2p.fitness > 0.01:\n",
    "    print(\"⚠️ Moderate ICP convergence (fitness > 0.01)\")\nelse:\n",
    "    print(\"❌ Poor ICP convergence (fitness < 0.01)\")\n",
    "    print(\"   This suggests fundamental coordinate system issues\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Apply final transformation\n",
    "source_pcd_aligned = source_pcd_coarse.transform(reg_p2p.transformation)\n",
    "\n",
    "print(f\"\\n🎯 FINAL TRANSFORMATION\")\n",
    "print(\"Transformation matrix:\")\n",
    "print(reg_p2p.transformation)\n",
    "\n",
    "# Extract rotation and translation components\n",
    "rotation_matrix = reg_p2p.transformation[:3, :3]\n",
    "translation_vector = reg_p2p.transformation[:3, 3]\n",
    "\n",
    "print(f\"\\nTranslation: [{translation_vector[0]:.3f}, {translation_vector[1]:.3f}, {translation_vector[2]:.3f}] meters\")\n",
    "print(f\"Translation magnitude: {np.linalg.norm(translation_vector):.3f} meters\")\n",
    "\n",
    "# Calculate rotation angles (approximate)\n",
    "import math\n",
    "rotation_x = math.atan2(rotation_matrix[2, 1], rotation_matrix[2, 2]) * 180 / math.pi\n",
    "rotation_y = math.atan2(-rotation_matrix[2, 0], math.sqrt(rotation_matrix[2, 1]**2 + rotation_matrix[2, 2]**2)) * 180 / math.pi\n",
    "rotation_z = math.atan2(rotation_matrix[1, 0], rotation_matrix[0, 0]) * 180 / math.pi\n",
    "\n",
    "print(f\"Rotation (approx): X={rotation_x:.2f}°, Y={rotation_y:.2f}°, Z={rotation_z:.2f}°\")\n",
    "print(f\"Total rotation magnitude: {math.sqrt(rotation_x**2 + rotation_y**2 + rotation_z**2):.2f}°\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 4. Visualize Results (Before/after alignment)"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "if enable_visualization:\n",
    "    # Create visualization\n",
    "    fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n",
    "    fig.suptitle(f'ICP Alignment Results with Coordinate Correction - {ground_method.upper()}', fontsize=16)\n",
    "    \n",
    "    # Get point arrays for plotting\n",
    "    drone_pts = np.asarray(target_pcd.points)\n",
    "    ifc_original_pts = np.asarray(ifc_pcd_original.points)\n",
    "    ifc_aligned_pts = np.asarray(source_pcd_aligned.points)\n",
    "    \n",
    "    # Plot 1: Before alignment (XY view)\n",
    "    axes[0, 0].scatter(drone_pts[::10, 0], drone_pts[::10, 1], c='blue', alpha=0.6, s=1, label='Drone (target)')\n",
    "    axes[0, 0].scatter(ifc_original_pts[::100, 0], ifc_original_pts[::100, 1], c='red', alpha=0.6, s=1, label='IFC (original)')\n",
    "    axes[0, 0].set_title('Before Alignment (XY view)')\n",
    "    axes[0, 0].set_xlabel('X (m)')\n",
    "    axes[0, 0].set_ylabel('Y (m)')\n",
    "    axes[0, 0].legend()\n",
    "    axes[0, 0].grid(True, alpha=0.3)\n",
    "    axes[0, 0].axis('equal')\n",
    "    \n",
    "    # Plot 2: After alignment (XY view)\n",
    "    axes[0, 1].scatter(drone_pts[::10, 0], drone_pts[::10, 1], c='blue', alpha=0.6, s=1, label='Drone (target)')\n",
    "    axes[0, 1].scatter(ifc_aligned_pts[::100, 0], ifc_aligned_pts[::100, 1], c='green', alpha=0.6, s=1, label='IFC (aligned)')\n",
    "    axes[0, 1].set_title('After Alignment (XY view)')\n",
    "    axes[0, 1].set_xlabel('X (m)')\n",
    "    axes[0, 1].set_ylabel('Y (m)')\n",
    "    axes[0, 1].legend()\n",
    "    axes[0, 1].grid(True, alpha=0.3)\n",
    "    axes[0, 1].axis('equal')\n",
    "    \n",
    "    # Plot 3: Before alignment (XZ view)\n",
    "    axes[1, 0].scatter(drone_pts[::10, 0], drone_pts[::10, 2], c='blue', alpha=0.6, s=1, label='Drone (target)')\n",
    "    axes[1, 0].scatter(ifc_original_pts[::100, 0], ifc_original_pts[::100, 2], c='red', alpha=0.6, s=1, label='IFC (original)')\n",
    "    axes[1, 0].set_title('Before Alignment (XZ view)')\n",
    "    axes[1, 0].set_xlabel('X (m)')\n",
    "    axes[1, 0].set_ylabel('Z (m)')\n",
    "    axes[1, 0].legend()\n",
    "    axes[1, 0].grid(True, alpha=0.3)\n",
    "    \n",
    "    # Plot 4: After alignment (XZ view)\n",
    "    axes[1, 1].scatter(drone_pts[::10, 0], drone_pts[::10, 2], c='blue', alpha=0.6, s=1, label='Drone (target)')\n",
    "    axes[1, 1].scatter(ifc_aligned_pts[::100, 0], ifc_aligned_pts[::100, 2], c='green', alpha=0.6, s=1, label='IFC (aligned)')\n",
    "    axes[1, 1].set_title('After Alignment (XZ view)')\n",
    "    axes[1, 1].set_xlabel('X (m)')\n",
    "    axes[1, 1].set_ylabel('Z (m)')\n",
    "    axes[1, 1].legend()\n",
    "    axes[1, 1].grid(True, alpha=0.3)\n",
    "    \n",
    "    plt.tight_layout()\n",
    "    \n",
    "    if save_results:\n",
    "        plt.savefig(output_path / f'icp_alignment_corrected_{ground_method}.png', dpi=300, bbox_inches='tight')\n",
    "        print(f\"📊 Saved visualization to: {output_path / f'icp_alignment_corrected_{ground_method}.png'}\")\n",
    "    \n",
    "    "    plt.show()"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 5. Compute Errors (RMSE, max deviation, error distribution)"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Compute alignment errors\n",
    "print(\"\\n📏 COMPUTING ALIGNMENT ERRORS\")\n",
    "\n",
    "# Get aligned points\n",
    "ifc_aligned_points = np.asarray(source_pcd_aligned.points)\n",
    "drone_points_target = np.asarray(target_pcd.points)\n",
    "\n",
    "# Find nearest neighbors for error computation\n",
    "print(\"Finding nearest neighbors...\")\n",
    "tree = cKDTree(drone_points_target)\n",
    "distances, indices = tree.query(ifc_aligned_points, k=1)\n",
    "\n",
    "# Compute error statistics\n",
    "rmse = np.sqrt(np.mean(distances**2))\n",
    "max_error = np.max(distances)\n",
    "mean_error = np.mean(distances)\n",
    "std_error = np.std(distances)\n",
    "median_error = np.median(distances)\n",
    "\n",
    "# Percentile errors\n",
    "p90_error = np.percentile(distances, 90)\n",
    "p95_error = np.percentile(distances, 95)\n",
    "p99_error = np.percentile(distances, 99)\n",
    "\n",
    "print(f\"\\n📊 ALIGNMENT ERROR STATISTICS\")\n",
    "print(f\"RMSE: {rmse:.6f} meters\")\n",
    "print(f\"Mean error: {mean_error:.6f} meters\")\n",
    "print(f\"Median error: {median_error:.6f} meters\")\n",
    "print(f\"Std deviation: {std_error:.6f} meters\")\n",
    "print(f\"Max error: {max_error:.6f} meters\")\n",
    "print(f\"\\nPercentile errors:\")\n",
    "print(f\"  90th percentile: {p90_error:.6f} meters\")\n",
    "print(f\"  95th percentile: {p95_error:.6f} meters\")\n",
    "print(f\"  99th percentile: {p99_error:.6f} meters\")\n",
    "\n",
    "# Quality assessment\n",
    "print(f\"\\n🎯 QUALITY ASSESSMENT\")\n",
    "if rmse < 0.5:\n",
    "    quality_status = \"✅ EXCELLENT (RMSE < 0.5m)\"\nelif rmse < 1.0:\n",
    "    quality_status = \"✅ GOOD (RMSE < 1.0m)\"\nelif rmse < 2.0:\n",
    "    quality_status = \"⚠️ ACCEPTABLE (RMSE < 2.0m)\"\nelse:\n",
    "    quality_status = \"❌ POOR (RMSE > 2.0m)\"\n",
    "\n",
    "print(f\"Alignment quality: {quality_status}\")\n",
    "print(f\"ICP fitness: {reg_p2p.fitness:.6f} {'(Good)' if reg_p2p.fitness > 0.1 else '(Poor)'}\")\n",
    "print(f\"Processing time: {icp_time:.2f} seconds\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Error distribution visualization\n",
    "if enable_visualization:\n",
    "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))\n",
    "    \n",
    "    # Histogram of errors\n",
    "    ax1.hist(distances, bins=50, alpha=0.7, color='skyblue', edgecolor='black')\n",
    "    ax1.axvline(rmse, color='red', linestyle='--', linewidth=2, label=f'RMSE: {rmse:.3f}m')\n",
    "    ax1.axvline(median_error, color='green', linestyle='--', linewidth=2, label=f'Median: {median_error:.3f}m')\n",
    "    ax1.set_xlabel('Distance Error (m)')\n",
    "    ax1.set_ylabel('Frequency')\n",
    "    ax1.set_title('Error Distribution')\n",
    "    ax1.legend()\n",
    "    ax1.grid(True, alpha=0.3)\n",
    "    \n",
    "    # Cumulative distribution\n",
    "    sorted_distances = np.sort(distances)\n",
    "    cumulative = np.arange(1, len(sorted_distances) + 1) / len(sorted_distances) * 100\n",
    "    ax2.plot(sorted_distances, cumulative, linewidth=2, color='blue')\n",
    "    ax2.axvline(rmse, color='red', linestyle='--', linewidth=2, label=f'RMSE: {rmse:.3f}m')\n",
    "    ax2.axhline(90, color='orange', linestyle='--', alpha=0.7, label='90th percentile')\n",
    "    ax2.axhline(95, color='red', linestyle='--', alpha=0.7, label='95th percentile')\n",
    "    ax2.set_xlabel('Distance Error (m)')\n",
    "    ax2.set_ylabel('Cumulative Percentage (%)')\n",
    "    ax2.set_title('Cumulative Error Distribution')\n",
    "    ax2.legend()\n",
    "    ax2.grid(True, alpha=0.3)\n",
    "    \n",
    "    plt.tight_layout()\n",
    "    \n",
    "    if save_results:\n",
    "        plt.savefig(output_path / f'error_distribution_corrected_{ground_method}.png', dpi=300, bbox_inches='tight')\n",
    "        print(f\"📊 Saved error distribution to: {output_path / f'error_distribution_corrected_{ground_method}.png'}\")\n",
    "    \n",
    "    plt.show()"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 6. Save Results"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "if save_results:\n",
    "    print(\"\\n💾 SAVING RESULTS\")\n",
    "    \n",
    "    # Save aligned point cloud\n",
    "    aligned_file = output_path / f'icp_aligned_corrected_{ground_method}.ply'\n",
    "    o3d.io.write_point_cloud(str(aligned_file), source_pcd_aligned)\n",
    "    print(f\"✅ Saved aligned point cloud: {aligned_file}\")\n",
    "    \n",
    "    # Save transformation matrix\n",
    "    transform_file = output_path / f'icp_transformation_corrected_{ground_method}.npy'\n",
    "    \n",
    "    # Combine coarse and fine transformations\n",
    "    coarse_transform = np.eye(4)\n",
    "    coarse_transform[:3, 3] = coarse_translation\n",
    "    if z_shift != 0:\n",
    "        z_shift_transform = np.eye(4)\n",
    "        z_shift_transform[2, 3] = z_shift\n",
    "        total_transform = reg_p2p.transformation @ coarse_transform @ z_shift_transform\n",
    "    else:\n",
    "        total_transform = reg_p2p.transformation @ coarse_transform\n",
    "    \n",
    "    np.save(transform_file, total_transform)\n",
    "    print(f\"✅ Saved transformation matrix: {transform_file}\")\n",
    "    \n",
    "    # Save comprehensive metrics\n",
    "    metrics = {\n",
    "        'timestamp': datetime.now().isoformat(),\n",
    "        'site_name': site_name,\n",
    "        'ground_method': ground_method,\n",
    "        'coordinate_correction_used': 'corrected' in str(drone_file),\n",
    "        'coordinate_status': coordinate_status,\n",
    "        \n",
    "        'input_data': {\n",
    "            'drone_points': int(len(drone_points)),\n",
    "            'ifc_points': int(len(ifc_points)),\n",
    "            'drone_file': str(drone_file),\n",
    "            'ifc_file': str(ifc_file)\n",
    "        },\n",
    "        \n",
    "        'initial_alignment': {\n",
    "            'initial_offset_magnitude': float(np.linalg.norm(initial_offset)),\n",
    "            'coarse_translation': coarse_translation.tolist(),\n",
    "            'z_shift_applied': float(z_shift)\n",
    "        },\n",
    "        \n",
    "        'icp_results': {\n",
    "            'fitness': float(reg_p2p.fitness),\n",
    "            'inlier_rmse': float(reg_p2p.inlier_rmse),\n",
    "            'processing_time_seconds': float(icp_time),\n",
    "            'max_iterations': icp_max_iterations,\n",
    "            'tolerance': icp_tolerance\n",
    "        },\n",
    "        \n",
    "        'transformation': {\n",
    "            'matrix': total_transform.tolist(),\n",
    "            'translation': translation_vector.tolist(),\n",
    "            'translation_magnitude': float(np.linalg.norm(translation_vector)),\n",
    "            'rotation_degrees': {\n",
    "                'x': float(rotation_x),\n",
    "                'y': float(rotation_y),\n",
    "                'z': float(rotation_z)\n",
    "            }\n",
    "        },\n",
    "        \n",
    "        'error_statistics': {\n",
    "            'rmse': float(rmse),\n",
    "            'mean_error': float(mean_error),\n",
    "            'median_error': float(median_error),\n",
    "            'std_error': float(std_error),\n",
    "            'max_error': float(max_error),\n",
    "            'percentiles': {\n",
    "                '90th': float(p90_error),\n",
    "                '95th': float(p95_error),\n",
    "                '99th': float(p99_error)\n",
    "            }\n",
    "        },\n",
    "        \n",
    "        'quality_assessment': {\n",
    "            'status': quality_status,\n",
    "            'rmse_category': 'excellent' if rmse < 0.5 else 'good' if rmse < 1.0 else 'acceptable' if rmse < 2.0 else 'poor',\n",
    "            'fitness_category': 'good' if reg_p2p.fitness > 0.1 else 'moderate' if reg_p2p.fitness > 0.01 else 'poor'\n",
    "        }\n",
    "    }\n",
    "    \n",
    "    metrics_file = output_path / f'icp_metrics_corrected_{ground_method}.json'\n",
    "    with open(metrics_file, 'w') as f:\n",
    "        json.dump(metrics, f, indent=2)\n",
    "    print(f\"✅ Saved metrics: {metrics_file}\")\n",
    "    \n",
    "    print(f\"\\n🎯 All results saved to: {output_path}\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Final summary\n",
    "print(\"\\n\" + \"=\"*80)\n",
    "print(\"🎯 ICP ALIGNMENT WITH COORDINATE CORRECTION - SUMMARY\")\n",
    "print(\"=\"*80)\n",
    "print(f\"Site: {site_name}\")\n",
    "print(f\"Ground method: {ground_method}\")\n",
    "print(f\"Coordinate correction: {'✅ Applied' if 'corrected' in str(drone_file) else '❌ Not applied'}\")\n",
    "print(f\"\\n📊 RESULTS:\")\n",
    "print(f\"  RMSE: {rmse:.6f} meters ({quality_status.split()[1]})\")\n",
    "print(f\"  ICP Fitness: {reg_p2p.fitness:.6f} ({'Good' if reg_p2p.fitness > 0.1 else 'Poor'})\")\n",
    "print(f\"  Max Error: {max_error:.6f} meters\")\n",
    "print(f\"  Processing Time: {icp_time:.2f} seconds\")\n",
    "print(f\"\\n🔄 COMPARISON WITH ORIGINAL ICP:\")\n",
    "print(f\"  Expected improvement: 6.64-19.37m → {rmse:.3f}m\")\n",
    "print(f\"  Improvement factor: {6.64/rmse:.1f}x - {19.37/rmse:.1f}x better\")\n",
    "print(f\"\\n✅ SUCCESS CRITERIA:\")\n",
    "print(f\"  RMSE < 1.0m: {'✅ Met' if rmse < 1.0 else '❌ Not met'}\")\n",
    "print(f\"  ICP Fitness > 0.1: {'✅ Met' if reg_p2p.fitness > 0.1 else '❌ Not met'}\")\n",
    "print(f\"  Visual alignment: {'✅ Check plots above' if enable_visualization else 'Disabled'}\")\n",
    "print(f\"\\n🎯 NEXT STEPS:\")\n",
    "if rmse < 1.0 and reg_p2p.fitness > 0.1:\n",
    "    print(f\"  ✅ Alignment successful! Ready for feature-based refinement\")\n",
    "    print(f\"  ✅ Proceed with pile detection integration\")\n",
    "    print(f\"  ✅ Use aligned point clouds for as-built vs as-planned validation\")\nelse:\n",
    "    print(f\"  ⚠️ Consider additional coordinate correction or feature-based alignment\")\n",
    "    print(f\"  ⚠️ Check coordinate system compatibility\")\n",
    "    print(f\"  ⚠️ Verify point cloud quality and preprocessing\")\n",
    "print(\"=\"*80)"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.8.5"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 4
}"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.8.5"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 4
}
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.8.5"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 4
}