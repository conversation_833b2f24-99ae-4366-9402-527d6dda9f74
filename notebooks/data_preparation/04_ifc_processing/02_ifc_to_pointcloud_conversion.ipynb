{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# IFC to Point Cloud Conversion\n", "\n", "## Overview\n", "\n", "This notebook converts IFC geometry to point clouds using a data-driven mesh-based approach:\n", "- Extract 3D meshes from IFC elements\n", "- Convert meshes to dense point clouds\n", "- Apply coordinate transformations\n", "- Export in standard point cloud formats\n", "\n", "**Method**: ifcopenshell + mesh extraction for reliable geometry conversion\n", "\n", "**Output**: PLY point cloud files ready for alignment and analysis\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Parameters\n", "ifc_file_path = \"/Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/raw/trino_enel/ifc/GRE.EEC.S.00.IT.P.14353.00.265.ifc\"\n", "output_dir = \"../../data/processed/trino_enel/ifc_pointclouds\"\n", "site_name = \"trino_enel\"\n", "\n", "# Point cloud generation parameters\n", "point_density = 0.1  # Point spacing in meters\n", "include_element_types = [\"IfcPile\", \"IfcBeam\", \"IfcColumn\", \"IfcSlab\"]\n", "min_element_size = 0.5  # Minimum element size to include (meters)\n", "max_points_per_element = 10000  # Limit points per element"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["IFC TO POINT CLOUD CONVERSION\n", "IFC file: GRE.EEC.S.00.IT.P.14353.00.265.ifc\n", "Output directory: ../../data/processed/trino_enel/ifc_pointclouds\n", "Point density: 0.1m\n"]}], "source": ["import ifcopenshell\n", "import ifcopenshell.geom\n", "import numpy as np\n", "import open3d as o3d\n", "from pathlib import Path\n", "import json\n", "import logging\n", "from typing import List, Tuple, Optional\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Setup logging\n", "logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')\n", "logger = logging.getLogger(__name__)\n", "\n", "# Setup output directory\n", "output_path = Path(output_dir)\n", "output_path.mkdir(parents=True, exist_ok=True)\n", "\n", "print(\"IFC TO POINT CLOUD CONVERSION\")\n", "print(f\"IFC file: {Path(ifc_file_path).name}\")\n", "print(f\"Output directory: {output_path}\")\n", "print(f\"Point density: {point_density}m\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Load IFC and Setup Geometry Processing"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading IFC file...\n", "IFC Schema: IFC4\n", "  IfcPile: 0 elements\n", "  IfcBeam: 0 elements\n", "  IfcColumn: 14460 elements\n", "  IfcSlab: 0 elements\n", "\n", "Total elements to convert: 14460\n"]}], "source": ["# Load IFC file\n", "print(\"Loading IFC file...\")\n", "ifc_file = ifcopenshell.open(ifc_file_path)\n", "\n", "# Setup geometry settings for mesh extraction\n", "settings = ifcopenshell.geom.settings()\n", "settings.set(settings.USE_WORLD_COORDS, True)\n", "settings.set(settings.WELD_VERTICES, True)\n", "#settings.set(settings.SEW_SHELLS, True)\n", "\n", "print(f\"IFC Schema: {ifc_file.schema}\")\n", "\n", "# Count elements to process\n", "total_elements = 0\n", "for element_type in include_element_types:\n", "    elements = ifc_file.by_type(element_type)\n", "    total_elements += len(elements)\n", "    print(f\"  {element_type}: {len(elements)} elements\")\n", "\n", "print(f\"\\nTotal elements to convert: {total_elements}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. <PERSON><PERSON> to Point Cloud Conversion Functions"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Point cloud conversion functions ready\n"]}], "source": ["def extract_mesh_from_element(element, settings):\n", "    \"\"\"Extract mesh geometry from IFC element.\"\"\"\n", "    try:\n", "        shape = ifcopenshell.geom.create_shape(settings, element)\n", "        if shape and shape.geometry:\n", "            # Get vertices and faces\n", "            vertices = np.array(shape.geometry.verts).reshape(-1, 3)\n", "            faces = np.array(shape.geometry.faces).reshape(-1, 3)\n", "            \n", "            if len(vertices) > 0 and len(faces) > 0:\n", "                return vertices, faces\n", "    except Exception as e:\n", "        logger.warning(f\"Could not extract mesh for {element.GlobalId}: {str(e)[:50]}...\")\n", "    \n", "    return None, None\n", "\n", "def mesh_to_pointcloud(vertices, faces, point_density=0.1, max_points=10000):\n", "    \"\"\"Convert mesh to point cloud using surface sampling.\"\"\"\n", "    try:\n", "        # Create Open3D mesh\n", "        mesh = o3d.geometry.TriangleMesh()\n", "        mesh.vertices = o3d.utility.Vector3dVector(vertices)\n", "        mesh.triangles = o3d.utility.Vector3iVector(faces)\n", "        \n", "        # Calculate number of points based on surface area and density\n", "        mesh.compute_triangle_normals()\n", "        surface_area = mesh.get_surface_area()\n", "        num_points = min(int(surface_area / (point_density ** 2)), max_points)\n", "        \n", "        if num_points < 10:\n", "            num_points = 10  # Minimum points\n", "        \n", "        # Sample points from mesh surface\n", "        point_cloud = mesh.sample_points_uniformly(number_of_points=num_points)\n", "        \n", "        return np.asarray(point_cloud.points)\n", "        \n", "    except Exception as e:\n", "        logger.warning(f\"Mesh to point cloud conversion failed: {e}\")\n", "        return None\n", "\n", "def filter_element_by_size(vertices, min_size=0.5):\n", "    \"\"\"Filter elements by minimum size.\"\"\"\n", "    if vertices is None or len(vertices) == 0:\n", "        return False\n", "    \n", "    # Calculate bounding box dimensions\n", "    bbox_min = np.min(vertices, axis=0)\n", "    bbox_max = np.max(vertices, axis=0)\n", "    dimensions = bbox_max - bbox_min\n", "    \n", "    # Check if any dimension is larger than minimum size\n", "    return np.any(dimensions > min_size)\n", "\n", "print(\"Point cloud conversion functions ready\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Convert IFC Elements to Point Cloud"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Converting IFC elements to point clouds...\n", "\n", "Processing 14460 IfcColumn elements...\n", "  Processed 50/14460 IfcColumn elements\n", "  Processed 100/14460 IfcColumn elements\n", "  Processed 150/14460 IfcColumn elements\n", "  Processed 200/14460 IfcColumn elements\n", "  Processed 250/14460 IfcColumn elements\n", "  Processed 300/14460 IfcColumn elements\n", "  Processed 350/14460 IfcColumn elements\n", "  Processed 400/14460 IfcColumn elements\n", "  Processed 450/14460 IfcColumn elements\n", "  Processed 500/14460 IfcColumn elements\n", "  Processed 550/14460 IfcColumn elements\n", "  Processed 600/14460 IfcColumn elements\n", "  Processed 650/14460 IfcColumn elements\n", "  Processed 700/14460 IfcColumn elements\n", "  Processed 750/14460 IfcColumn elements\n", "  Processed 800/14460 IfcColumn elements\n", "  Processed 850/14460 IfcColumn elements\n", "  Processed 900/14460 IfcColumn elements\n", "  Processed 950/14460 IfcColumn elements\n", "  Processed 1000/14460 IfcColumn elements\n", "  Processed 1050/14460 IfcColumn elements\n", "  Processed 1100/14460 IfcColumn elements\n", "  Processed 1150/14460 IfcColumn elements\n", "  Processed 1200/14460 IfcColumn elements\n", "  Processed 1250/14460 IfcColumn elements\n", "  Processed 1300/14460 IfcColumn elements\n", "  Processed 1350/14460 IfcColumn elements\n", "  Processed 1400/14460 IfcColumn elements\n", "  Processed 1450/14460 IfcColumn elements\n", "  Processed 1500/14460 IfcColumn elements\n", "  Processed 1550/14460 IfcColumn elements\n", "  Processed 1600/14460 IfcColumn elements\n", "  Processed 1650/14460 IfcColumn elements\n", "  Processed 1700/14460 IfcColumn elements\n", "  Processed 1750/14460 IfcColumn elements\n", "  Processed 1800/14460 IfcColumn elements\n", "  Processed 1850/14460 IfcColumn elements\n", "  Processed 1900/14460 IfcColumn elements\n", "  Processed 1950/14460 IfcColumn elements\n", "  Processed 2000/14460 IfcColumn elements\n", "  Processed 2050/14460 IfcColumn elements\n", "  Processed 2100/14460 IfcColumn elements\n", "  Processed 2150/14460 IfcColumn elements\n", "  Processed 2200/14460 IfcColumn elements\n", "  Processed 2250/14460 IfcColumn elements\n", "  Processed 2300/14460 IfcColumn elements\n", "  Processed 2350/14460 IfcColumn elements\n", "  Processed 2400/14460 IfcColumn elements\n", "  Processed 2450/14460 IfcColumn elements\n", "  Processed 2500/14460 IfcColumn elements\n", "  Processed 2550/14460 IfcColumn elements\n", "  Processed 2600/14460 IfcColumn elements\n", "  Processed 2650/14460 IfcColumn elements\n", "  Processed 2700/14460 IfcColumn elements\n", "  Processed 2750/14460 IfcColumn elements\n", "  Processed 2800/14460 IfcColumn elements\n", "  Processed 2850/14460 IfcColumn elements\n", "  Processed 2900/14460 IfcColumn elements\n", "  Processed 2950/14460 IfcColumn elements\n", "  Processed 3000/14460 IfcColumn elements\n", "  Processed 3050/14460 IfcColumn elements\n", "  Processed 3100/14460 IfcColumn elements\n", "  Processed 3150/14460 IfcColumn elements\n", "  Processed 3200/14460 IfcColumn elements\n", "  Processed 3250/14460 IfcColumn elements\n", "  Processed 3300/14460 IfcColumn elements\n", "  Processed 3350/14460 IfcColumn elements\n", "  Processed 3400/14460 IfcColumn elements\n", "  Processed 3450/14460 IfcColumn elements\n", "  Processed 3500/14460 IfcColumn elements\n", "  Processed 3550/14460 IfcColumn elements\n", "  Processed 3600/14460 IfcColumn elements\n", "  Processed 3650/14460 IfcColumn elements\n", "  Processed 3700/14460 IfcColumn elements\n", "  Processed 3750/14460 IfcColumn elements\n", "  Processed 3800/14460 IfcColumn elements\n", "  Processed 3850/14460 IfcColumn elements\n", "  Processed 3900/14460 IfcColumn elements\n", "  Processed 3950/14460 IfcColumn elements\n", "  Processed 4000/14460 IfcColumn elements\n", "  Processed 4050/14460 IfcColumn elements\n", "  Processed 4100/14460 IfcColumn elements\n", "  Processed 4150/14460 IfcColumn elements\n", "  Processed 4200/14460 IfcColumn elements\n", "  Processed 4250/14460 IfcColumn elements\n", "  Processed 4300/14460 IfcColumn elements\n", "  Processed 4350/14460 IfcColumn elements\n", "  Processed 4400/14460 IfcColumn elements\n", "  Processed 4450/14460 IfcColumn elements\n", "  Processed 4500/14460 IfcColumn elements\n", "  Processed 4550/14460 IfcColumn elements\n", "  Processed 4600/14460 IfcColumn elements\n", "  Processed 4650/14460 IfcColumn elements\n", "  Processed 4700/14460 IfcColumn elements\n", "  Processed 4750/14460 IfcColumn elements\n", "  Processed 4800/14460 IfcColumn elements\n", "  Processed 4850/14460 IfcColumn elements\n", "  Processed 4900/14460 IfcColumn elements\n", "  Processed 4950/14460 IfcColumn elements\n", "  Processed 5000/14460 IfcColumn elements\n", "  Processed 5050/14460 IfcColumn elements\n", "  Processed 5100/14460 IfcColumn elements\n", "  Processed 5150/14460 IfcColumn elements\n", "  Processed 5200/14460 IfcColumn elements\n", "  Processed 5250/14460 IfcColumn elements\n", "  Processed 5300/14460 IfcColumn elements\n", "  Processed 5350/14460 IfcColumn elements\n", "  Processed 5400/14460 IfcColumn elements\n", "  Processed 5450/14460 IfcColumn elements\n", "  Processed 5500/14460 IfcColumn elements\n", "  Processed 5550/14460 IfcColumn elements\n", "  Processed 5600/14460 IfcColumn elements\n", "  Processed 5650/14460 IfcColumn elements\n", "  Processed 5700/14460 IfcColumn elements\n", "  Processed 5750/14460 IfcColumn elements\n", "  Processed 5800/14460 IfcColumn elements\n", "  Processed 5850/14460 IfcColumn elements\n", "  Processed 5900/14460 IfcColumn elements\n", "  Processed 5950/14460 IfcColumn elements\n", "  Processed 6000/14460 IfcColumn elements\n", "  Processed 6050/14460 IfcColumn elements\n", "  Processed 6100/14460 IfcColumn elements\n", "  Processed 6150/14460 IfcColumn elements\n", "  Processed 6200/14460 IfcColumn elements\n", "  Processed 6250/14460 IfcColumn elements\n", "  Processed 6300/14460 IfcColumn elements\n", "  Processed 6350/14460 IfcColumn elements\n", "  Processed 6400/14460 IfcColumn elements\n", "  Processed 6450/14460 IfcColumn elements\n", "  Processed 6500/14460 IfcColumn elements\n", "  Processed 6550/14460 IfcColumn elements\n", "  Processed 6600/14460 IfcColumn elements\n", "  Processed 6650/14460 IfcColumn elements\n", "  Processed 6700/14460 IfcColumn elements\n", "  Processed 6750/14460 IfcColumn elements\n", "  Processed 6800/14460 IfcColumn elements\n", "  Processed 6850/14460 IfcColumn elements\n", "  Processed 6900/14460 IfcColumn elements\n", "  Processed 6950/14460 IfcColumn elements\n", "  Processed 7000/14460 IfcColumn elements\n", "  Processed 7050/14460 IfcColumn elements\n", "  Processed 7100/14460 IfcColumn elements\n", "  Processed 7150/14460 IfcColumn elements\n", "  Processed 7200/14460 IfcColumn elements\n", "  Processed 7250/14460 IfcColumn elements\n", "  Processed 7300/14460 IfcColumn elements\n", "  Processed 7350/14460 IfcColumn elements\n", "  Processed 7400/14460 IfcColumn elements\n", "  Processed 7450/14460 IfcColumn elements\n", "  Processed 7500/14460 IfcColumn elements\n", "  Processed 7550/14460 IfcColumn elements\n", "  Processed 7600/14460 IfcColumn elements\n", "  Processed 7650/14460 IfcColumn elements\n", "  Processed 7700/14460 IfcColumn elements\n", "  Processed 7750/14460 IfcColumn elements\n", "  Processed 7800/14460 IfcColumn elements\n", "  Processed 7850/14460 IfcColumn elements\n", "  Processed 7900/14460 IfcColumn elements\n", "  Processed 7950/14460 IfcColumn elements\n", "  Processed 8000/14460 IfcColumn elements\n", "  Processed 8050/14460 IfcColumn elements\n", "  Processed 8100/14460 IfcColumn elements\n", "  Processed 8150/14460 IfcColumn elements\n", "  Processed 8200/14460 IfcColumn elements\n", "  Processed 8250/14460 IfcColumn elements\n", "  Processed 8300/14460 IfcColumn elements\n", "  Processed 8350/14460 IfcColumn elements\n", "  Processed 8400/14460 IfcColumn elements\n", "  Processed 8450/14460 IfcColumn elements\n", "  Processed 8500/14460 IfcColumn elements\n", "  Processed 8550/14460 IfcColumn elements\n", "  Processed 8600/14460 IfcColumn elements\n", "  Processed 8650/14460 IfcColumn elements\n", "  Processed 8700/14460 IfcColumn elements\n", "  Processed 8750/14460 IfcColumn elements\n", "  Processed 8800/14460 IfcColumn elements\n", "  Processed 8850/14460 IfcColumn elements\n", "  Processed 8900/14460 IfcColumn elements\n", "  Processed 8950/14460 IfcColumn elements\n", "  Processed 9000/14460 IfcColumn elements\n", "  Processed 9050/14460 IfcColumn elements\n", "  Processed 9100/14460 IfcColumn elements\n", "  Processed 9150/14460 IfcColumn elements\n", "  Processed 9200/14460 IfcColumn elements\n", "  Processed 9250/14460 IfcColumn elements\n", "  Processed 9300/14460 IfcColumn elements\n", "  Processed 9350/14460 IfcColumn elements\n", "  Processed 9400/14460 IfcColumn elements\n", "  Processed 9450/14460 IfcColumn elements\n", "  Processed 9500/14460 IfcColumn elements\n", "  Processed 9550/14460 IfcColumn elements\n", "  Processed 9600/14460 IfcColumn elements\n", "  Processed 9650/14460 IfcColumn elements\n", "  Processed 9700/14460 IfcColumn elements\n", "  Processed 9750/14460 IfcColumn elements\n", "  Processed 9800/14460 IfcColumn elements\n", "  Processed 9850/14460 IfcColumn elements\n", "  Processed 9900/14460 IfcColumn elements\n", "  Processed 9950/14460 IfcColumn elements\n", "  Processed 10000/14460 IfcColumn elements\n", "  Processed 10050/14460 IfcColumn elements\n", "  Processed 10100/14460 IfcColumn elements\n", "  Processed 10150/14460 IfcColumn elements\n", "  Processed 10200/14460 IfcColumn elements\n", "  Processed 10250/14460 IfcColumn elements\n", "  Processed 10300/14460 IfcColumn elements\n", "  Processed 10350/14460 IfcColumn elements\n", "  Processed 10400/14460 IfcColumn elements\n", "  Processed 10450/14460 IfcColumn elements\n", "  Processed 10500/14460 IfcColumn elements\n", "  Processed 10550/14460 IfcColumn elements\n", "  Processed 10600/14460 IfcColumn elements\n", "  Processed 10650/14460 IfcColumn elements\n", "  Processed 10700/14460 IfcColumn elements\n", "  Processed 10750/14460 IfcColumn elements\n", "  Processed 10800/14460 IfcColumn elements\n", "  Processed 10850/14460 IfcColumn elements\n", "  Processed 10900/14460 IfcColumn elements\n", "  Processed 10950/14460 IfcColumn elements\n", "  Processed 11000/14460 IfcColumn elements\n", "  Processed 11050/14460 IfcColumn elements\n", "  Processed 11100/14460 IfcColumn elements\n", "  Processed 11150/14460 IfcColumn elements\n", "  Processed 11200/14460 IfcColumn elements\n", "  Processed 11250/14460 IfcColumn elements\n", "  Processed 11300/14460 IfcColumn elements\n", "  Processed 11350/14460 IfcColumn elements\n", "  Processed 11400/14460 IfcColumn elements\n", "  Processed 11450/14460 IfcColumn elements\n", "  Processed 11500/14460 IfcColumn elements\n", "  Processed 11550/14460 IfcColumn elements\n", "  Processed 11600/14460 IfcColumn elements\n", "  Processed 11650/14460 IfcColumn elements\n", "  Processed 11700/14460 IfcColumn elements\n", "  Processed 11750/14460 IfcColumn elements\n", "  Processed 11800/14460 IfcColumn elements\n", "  Processed 11850/14460 IfcColumn elements\n", "  Processed 11900/14460 IfcColumn elements\n", "  Processed 11950/14460 IfcColumn elements\n", "  Processed 12000/14460 IfcColumn elements\n", "  Processed 12050/14460 IfcColumn elements\n", "  Processed 12100/14460 IfcColumn elements\n", "  Processed 12150/14460 IfcColumn elements\n", "  Processed 12200/14460 IfcColumn elements\n", "  Processed 12250/14460 IfcColumn elements\n", "  Processed 12300/14460 IfcColumn elements\n", "  Processed 12350/14460 IfcColumn elements\n", "  Processed 12400/14460 IfcColumn elements\n", "  Processed 12450/14460 IfcColumn elements\n", "  Processed 12500/14460 IfcColumn elements\n", "  Processed 12550/14460 IfcColumn elements\n", "  Processed 12600/14460 IfcColumn elements\n", "  Processed 12650/14460 IfcColumn elements\n", "  Processed 12700/14460 IfcColumn elements\n", "  Processed 12750/14460 IfcColumn elements\n", "  Processed 12800/14460 IfcColumn elements\n", "  Processed 12850/14460 IfcColumn elements\n", "  Processed 12900/14460 IfcColumn elements\n", "  Processed 12950/14460 IfcColumn elements\n", "  Processed 13000/14460 IfcColumn elements\n", "  Processed 13050/14460 IfcColumn elements\n", "  Processed 13100/14460 IfcColumn elements\n", "  Processed 13150/14460 IfcColumn elements\n", "  Processed 13200/14460 IfcColumn elements\n", "  Processed 13250/14460 IfcColumn elements\n", "  Processed 13300/14460 IfcColumn elements\n", "  Processed 13350/14460 IfcColumn elements\n", "  Processed 13400/14460 IfcColumn elements\n", "  Processed 13450/14460 IfcColumn elements\n", "  Processed 13500/14460 IfcColumn elements\n", "  Processed 13550/14460 IfcColumn elements\n", "  Processed 13600/14460 IfcColumn elements\n", "  Processed 13650/14460 IfcColumn elements\n", "  Processed 13700/14460 IfcColumn elements\n", "  Processed 13750/14460 IfcColumn elements\n", "  Processed 13800/14460 IfcColumn elements\n", "  Processed 13850/14460 IfcColumn elements\n", "  Processed 13900/14460 IfcColumn elements\n", "  Processed 13950/14460 IfcColumn elements\n", "  Processed 14000/14460 IfcColumn elements\n", "  Processed 14050/14460 IfcColumn elements\n", "  Processed 14100/14460 IfcColumn elements\n", "  Processed 14150/14460 IfcColumn elements\n", "  Processed 14200/14460 IfcColumn elements\n", "  Processed 14250/14460 IfcColumn elements\n", "  Processed 14300/14460 IfcColumn elements\n", "  Processed 14350/14460 IfcColumn elements\n", "  Processed 14400/14460 IfcColumn elements\n", "  Processed 14450/14460 IfcColumn elements\n", "\n", "Conversion completed:\n", "  Total points generated: 1,359,240\n", "  Successful conversions: 14460\n", "  Failed conversions: 0\n", "  Small elements filtered: 0\n"]}], "source": ["# Convert elements to point clouds\n", "print(\"\\nConverting IFC elements to point clouds...\")\n", "\n", "all_points = []\n", "element_info = []\n", "conversion_stats = {\n", "    'total_processed': 0,\n", "    'successful_conversions': 0,\n", "    'failed_conversions': 0,\n", "    'filtered_small_elements': 0,\n", "    'total_points_generated': 0\n", "}\n", "\n", "for element_type in include_element_types:\n", "    elements = ifc_file.by_type(element_type)\n", "    if not elements:\n", "        continue\n", "        \n", "    print(f\"\\nProcessing {len(elements)} {element_type} elements...\")\n", "    \n", "    for i, element in enumerate(elements):\n", "        conversion_stats['total_processed'] += 1\n", "        \n", "        try:\n", "            # Extract mesh\n", "            vertices, faces = extract_mesh_from_element(element, settings)\n", "            \n", "            if vertices is not None and faces is not None:\n", "                # Filter by size\n", "                if not filter_element_by_size(vertices, min_element_size):\n", "                    conversion_stats['filtered_small_elements'] += 1\n", "                    continue\n", "                \n", "                # Convert to point cloud\n", "                points = mesh_to_pointcloud(vertices, faces, point_density, max_points_per_element)\n", "                \n", "                if points is not None and len(points) > 0:\n", "                    all_points.append(points)\n", "                    \n", "                    # Store element information\n", "                    element_info.append({\n", "                        'GlobalId': element.GlobalId,\n", "                        'Name': getattr(element, 'Name', None),\n", "                        'Type': element.is_a(),\n", "                        'PointCount': len(points),\n", "                        'BoundingBox': {\n", "                            'min': np.min(points, axis=0).tolist(),\n", "                            'max': np.max(points, axis=0).tolist()\n", "                        }\n", "                    })\n", "                    \n", "                    conversion_stats['successful_conversions'] += 1\n", "                    conversion_stats['total_points_generated'] += len(points)\n", "                else:\n", "                    conversion_stats['failed_conversions'] += 1\n", "            else:\n", "                conversion_stats['failed_conversions'] += 1\n", "                \n", "        except Exception as e:\n", "            logger.error(f\"Error converting {element_type} {element.GlobalId}: {e}\")\n", "            conversion_stats['failed_conversions'] += 1\n", "            continue\n", "        \n", "        # Progress indicator\n", "        if (i + 1) % 50 == 0:\n", "            print(f\"  Processed {i+1}/{len(elements)} {element_type} elements\")\n", "\n", "# <PERSON><PERSON><PERSON> all points\n", "if all_points:\n", "    combined_points = np.vstack(all_points)\n", "    print(f\"\\nConversion completed:\")\n", "    print(f\"  Total points generated: {len(combined_points):,}\")\n", "    print(f\"  Successful conversions: {conversion_stats['successful_conversions']}\")\n", "    print(f\"  Failed conversions: {conversion_stats['failed_conversions']}\")\n", "    print(f\"  Small elements filtered: {conversion_stats['filtered_small_elements']}\")\n", "else:\n", "    print(\"\\nNo points generated - conversion failed\")\n", "    combined_points = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Point Cloud Analysis and Export"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Point Cloud Analysis:\n", "  Total points: 1,359,240\n", "  Point cloud bounds:\n", "    X: 435267.17 to 436719.98\n", "    Y: 5010900.69 to 5012462.43\n", "    Z: 152.85 to 161.66\n", "  Center: [435986.35, 5011746.88, 157.33]\n", "  Extent: [1452.81, 1561.75, 8.81]\n", "\n", "Point cloud saved: ../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_pointcloud.ply\n", "Data-driven point cloud saved: ../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\n", "Element information saved: ../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_element_info.json\n", "Conversion summary saved: ../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_conversion_summary.json\n", "\n", "IFC TO POINT CLOUD CONVERSION COMPLETE\n", "Files generated in: ../../data/processed/trino_enel/ifc_pointclouds\n"]}], "source": ["import pandas as pd\n", "\n", "if combined_points is not None and len(combined_points) > 0:\n", "    # Analyze point cloud properties\n", "    print(f\"\\nPoint Cloud Analysis:\")\n", "    print(f\"  Total points: {len(combined_points):,}\")\n", "    print(f\"  Point cloud bounds:\")\n", "    print(f\"    X: {combined_points[:, 0].min():.2f} to {combined_points[:, 0].max():.2f}\")\n", "    print(f\"    Y: {combined_points[:, 1].min():.2f} to {combined_points[:, 1].max():.2f}\")\n", "    print(f\"    Z: {combined_points[:, 2].min():.2f} to {combined_points[:, 2].max():.2f}\")\n", "    \n", "    # Calculate point cloud statistics\n", "    center = np.mean(combined_points, axis=0)\n", "    extent = np.max(combined_points, axis=0) - np.min(combined_points, axis=0)\n", "    \n", "    print(f\"  Center: [{center[0]:.2f}, {center[1]:.2f}, {center[2]:.2f}]\")\n", "    print(f\"  Extent: [{extent[0]:.2f}, {extent[1]:.2f}, {extent[2]:.2f}]\")\n", "    \n", "    # Create Open3D point cloud\n", "    pcd = o3d.geometry.PointCloud()\n", "    pcd.points = o3d.utility.Vector3dVector(combined_points)\n", "    \n", "    # Generate output filename\n", "    ifc_filename = Path(ifc_file_path).stem\n", "    \n", "    # Save point cloud\n", "    pointcloud_path = output_path / f\"{ifc_filename}_pointcloud.ply\"\n", "    o3d.io.write_point_cloud(str(pointcloud_path), pcd)\n", "    print(f\"\\nPoint cloud saved: {pointcloud_path}\")\n", "    \n", "    # Save data-driven version (with element metadata)\n", "    datadriven_path = output_path / f\"{ifc_filename}_data_driven.ply\"\n", "    o3d.io.write_point_cloud(str(datadriven_path), pcd)\n", "    print(f\"Data-driven point cloud saved: {datadriven_path}\")\n", "    \n", "    # Save element information\n", "    element_info_path = output_path / f\"{ifc_filename}_element_info.json\"\n", "    with open(element_info_path, 'w') as f:\n", "        json.dump(element_info, f, indent=2)\n", "    print(f\"Element information saved: {element_info_path}\")\n", "    \n", "    # Create conversion summary\n", "    summary = {\n", "        'ifc_file': ifc_filename,\n", "        'conversion_date': pd.Timestamp.now().isoformat(),\n", "        'parameters': {\n", "            'point_density': point_density,\n", "            'min_element_size': min_element_size,\n", "            'max_points_per_element': max_points_per_element,\n", "            'element_types': include_element_types\n", "        },\n", "        'statistics': conversion_stats,\n", "        'point_cloud_properties': {\n", "            'total_points': len(combined_points),\n", "            'bounds': {\n", "                'x_min': float(combined_points[:, 0].min()),\n", "                'x_max': float(combined_points[:, 0].max()),\n", "                'y_min': float(combined_points[:, 1].min()),\n", "                'y_max': float(combined_points[:, 1].max()),\n", "                'z_min': float(combined_points[:, 2].min()),\n", "                'z_max': float(combined_points[:, 2].max())\n", "            },\n", "            'center': center.tolist(),\n", "            'extent': extent.tolist()\n", "        },\n", "        'files_generated': {\n", "            'point_cloud': str(pointcloud_path),\n", "            'data_driven': str(datadriven_path),\n", "            'element_info': str(element_info_path)\n", "        }\n", "    }\n", "    \n", "    summary_path = output_path / f\"{ifc_filename}_conversion_summary.json\"\n", "    with open(summary_path, 'w') as f:\n", "        json.dump(summary, f, indent=2)\n", "    \n", "    print(f\"Conversion summary saved: {summary_path}\")\n", "    print(f\"\\nIFC TO POINT CLOUD CONVERSION COMPLETE\")\n", "    print(f\"Files generated in: {output_path}\")\n", "    \n", "else:\n", "    print(\"\\nNo point cloud generated - conversion failed\")\n", "    print(\"Check IFC file and processing parameters\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}