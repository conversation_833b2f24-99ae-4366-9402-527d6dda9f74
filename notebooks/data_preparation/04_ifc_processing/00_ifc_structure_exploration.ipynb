# Parameters
ifc_file_path = "/Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/raw/trino_enel/ifc/GRE.EEC.S.00.IT.P.14353.00.265.ifc"
output_dir = "../../data/processed/trino_enel/ifc_exploration"

# Exploration parameters
max_elements_to_analyze = 10  # Limit for detailed analysis
show_detailed_properties = True
analyze_coordinate_systems = True

import ifcopenshell
import ifcopenshell.geom
import numpy as np
import pandas as pd
from pathlib import Path
import json
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

# Configure pandas for better display in exploration
pd.set_option('display.max_columns', None)
pd.set_option('display.width', None)
pd.set_option('display.max_colwidth', 30)
pd.set_option('display.precision', 3)

# Setup output directory
output_path = Path(output_dir)
output_path.mkdir(parents=True, exist_ok=True)

print("IFC STRUCTURE EXPLORATION")
print(f"Analyzing: {Path(ifc_file_path).name}")
print(f"Output: {output_path}")

# Load IFC file
print("Loading IFC file...")
ifc_file = ifcopenshell.open(ifc_file_path)

print(f"\n=== BASIC IFC INFORMATION ===")
print(f"Schema: {ifc_file.schema}")
print(f"Total entities: {len(list(ifc_file))}")

# Get project hierarchy
projects = ifc_file.by_type('IfcProject')
sites = ifc_file.by_type('IfcSite')
buildings = ifc_file.by_type('IfcBuilding')
building_storeys = ifc_file.by_type('IfcBuildingStorey')

print(f"\n=== PROJECT HIERARCHY ===")
print(f"Projects: {len(projects)}")
if projects:
    project = projects[0]
    print(f"  Name: {getattr(project, 'Name', 'Unnamed')}")
    print(f"  Description: {getattr(project, 'Description', 'No description')}")
    print(f"  GlobalId: {project.GlobalId}")

print(f"Sites: {len(sites)}")
if sites:
    site = sites[0]
    print(f"  Name: {getattr(site, 'Name', 'Unnamed')}")
    print(f"  Description: {getattr(site, 'Description', 'No description')}")
    print(f"  GlobalId: {site.GlobalId}")

print(f"Buildings: {len(buildings)}")
print(f"Building Storeys: {len(building_storeys)}")

# Discover all element types in the file
print("\n=== ELEMENT TYPE DISCOVERY ===")

# Count all entity types
entity_counts = defaultdict(int)
for entity in ifc_file:
    entity_counts[entity.is_a()] += 1

# Filter for building elements (typically start with 'Ifc' and are physical)
building_elements = {}
spatial_elements = {}
relationship_elements = {}
other_elements = {}

for entity_type, count in sorted(entity_counts.items()):
    if entity_type.startswith('IfcRel'):
        relationship_elements[entity_type] = count
    elif entity_type in ['IfcSite', 'IfcBuilding', 'IfcBuildingStorey', 'IfcSpace']:
        spatial_elements[entity_type] = count
    elif entity_type in ['IfcWall', 'IfcSlab', 'IfcBeam', 'IfcColumn', 'IfcPile', 'IfcRoof', 'IfcStair', 'IfcWindow', 'IfcDoor']:
        building_elements[entity_type] = count
    else:
        other_elements[entity_type] = count

print(f"\nBUILDING ELEMENTS ({len(building_elements)} types):")
for elem_type, count in sorted(building_elements.items(), key=lambda x: x[1], reverse=True):
    print(f"  {elem_type}: {count}")

print(f"\nSPATIAL ELEMENTS ({len(spatial_elements)} types):")
for elem_type, count in sorted(spatial_elements.items(), key=lambda x: x[1], reverse=True):
    print(f"  {elem_type}: {count}")

print(f"\nRELATIONSHIP ELEMENTS ({len(relationship_elements)} types):")
for elem_type, count in sorted(relationship_elements.items(), key=lambda x: x[1], reverse=True)[:10]:  # Top 10
    print(f"  {elem_type}: {count}")
if len(relationship_elements) > 10:
    print(f"  ... and {len(relationship_elements) - 10} more relationship types")

print(f"\nOTHER ELEMENTS ({len(other_elements)} types):")
for elem_type, count in sorted(other_elements.items(), key=lambda x: x[1], reverse=True)[:10]:  # Top 10
    print(f"  {elem_type}: {count}")
if len(other_elements) > 10:
    print(f"  ... and {len(other_elements) - 10} more element types")

# Analyze key building elements in detail
key_elements = ['IfcPile', 'IfcBeam', 'IfcColumn', 'IfcSlab', 'IfcWall']

print("\n=== DETAILED ELEMENT ANALYSIS ===")

for element_type in key_elements:
    elements = ifc_file.by_type(element_type)
    if not elements:
        print(f"\n{element_type}: Not found")
        continue
    
    print(f"\n{element_type}: {len(elements)} elements")
    
    # Analyze first few elements
    sample_size = min(max_elements_to_analyze, len(elements))
    print(f"Analyzing first {sample_size} elements...")
    
    for i, element in enumerate(elements[:sample_size]):
        print(f"\n  Element {i+1}:")
        print(f"    GlobalId: {element.GlobalId}")
        print(f"    Name: {getattr(element, 'Name', 'Unnamed')}")
        print(f"    Description: {getattr(element, 'Description', 'No description')}")
        print(f"    Tag: {getattr(element, 'Tag', 'No tag')}")
        
        # Check for geometry
        has_geometry = False
        if hasattr(element, 'ObjectPlacement') and element.ObjectPlacement:
            try:
                settings = ifcopenshell.geom.settings()
                shape = ifcopenshell.geom.create_shape(settings, element)
                if shape:
                    has_geometry = True
                    # Get transformation matrix
                    matrix = shape.transformation.matrix.data
                    transformation_matrix = np.array(matrix).reshape(4, 4)
                    position = transformation_matrix[:3, 3]
                    print(f"    Position: [{position[0]:.2f}, {position[1]:.2f}, {position[2]:.2f}]")
                    
                    # Get geometry info
                    vertices = shape.geometry.verts
                    if vertices:
                        vertices_array = np.array(vertices).reshape(-1, 3)
                        bbox_min = np.min(vertices_array, axis=0)
                        bbox_max = np.max(vertices_array, axis=0)
                        dimensions = bbox_max - bbox_min
                        print(f"    Dimensions: [{dimensions[0]:.2f}, {dimensions[1]:.2f}, {dimensions[2]:.2f}]")
            except Exception as e:
                print(f"    Geometry extraction failed: {str(e)[:50]}...")
        
        print(f"    Has Geometry: {has_geometry}")
        
        # Check for materials
        materials = []
        if hasattr(element, 'HasAssociations'):
            for association in element.HasAssociations:
                if association.is_a('IfcRelAssociatesMaterial'):
                    material = association.RelatingMaterial
                    if hasattr(material, 'Name'):
                        materials.append(material.Name)
        print(f"    Materials: {materials if materials else 'None'}")
        
        # Check for property sets
        property_sets = []
        if hasattr(element, 'IsDefinedBy'):
            for definition in element.IsDefinedBy:
                if definition.is_a('IfcRelDefinesByProperties'):
                    property_set = definition.RelatingPropertyDefinition
                    if property_set.is_a('IfcPropertySet'):
                        property_sets.append(property_set.Name)
        print(f"    Property Sets: {property_sets if property_sets else 'None'}")
        
        if i >= 2:  # Limit detailed output
            break
    
    if len(elements) > sample_size:
        print(f"  ... and {len(elements) - sample_size} more {element_type} elements")

# Analyze coordinate systems and spatial references
print("\n=== COORDINATE SYSTEM ANALYSIS ===")

# Look for coordinate reference systems
map_conversions = ifc_file.by_type('IfcMapConversion')
projected_crs = ifc_file.by_type('IfcProjectedCRS')
geometric_contexts = ifc_file.by_type('IfcGeometricRepresentationContext')

print(f"Map Conversions: {len(map_conversions)}")
if map_conversions:
    for i, conversion in enumerate(map_conversions):
        print(f"  Conversion {i+1}:")
        print(f"    Eastings: {getattr(conversion, 'Eastings', 'Not specified')}")
        print(f"    Northings: {getattr(conversion, 'Northings', 'Not specified')}")
        print(f"    OrthogonalHeight: {getattr(conversion, 'OrthogonalHeight', 'Not specified')}")
        print(f"    XAxisAbscissa: {getattr(conversion, 'XAxisAbscissa', 'Not specified')}")
        print(f"    XAxisOrdinate: {getattr(conversion, 'XAxisOrdinate', 'Not specified')}")
        print(f"    Scale: {getattr(conversion, 'Scale', 'Not specified')}")

print(f"\nProjected CRS: {len(projected_crs)}")
if projected_crs:
    for i, crs in enumerate(projected_crs):
        print(f"  CRS {i+1}:")
        print(f"    Name: {getattr(crs, 'Name', 'Unnamed')}")
        print(f"    Description: {getattr(crs, 'Description', 'No description')}")
        print(f"    GeodeticDatum: {getattr(crs, 'GeodeticDatum', 'Not specified')}")
        print(f"    MapProjection: {getattr(crs, 'MapProjection', 'Not specified')}")

print(f"\nGeometric Contexts: {len(geometric_contexts)}")
if geometric_contexts:
    for i, context in enumerate(geometric_contexts):
        print(f"  Context {i+1}:")
        print(f"    ContextType: {getattr(context, 'ContextType', 'Not specified')}")
        print(f"    CoordinateSpaceDimension: {getattr(context, 'CoordinateSpaceDimension', 'Not specified')}")
        print(f"    Precision: {getattr(context, 'Precision', 'Not specified')}")
        if hasattr(context, 'WorldCoordinateSystem') and context.WorldCoordinateSystem:
            wcs = context.WorldCoordinateSystem
            print(f"    World Coordinate System: {wcs.is_a()}")

# Analyze property sets across the model
print("\n=== PROPERTY SET ANALYSIS ===")

property_sets = ifc_file.by_type('IfcPropertySet')
print(f"Total Property Sets: {len(property_sets)}")

# Collect all property set names and their properties
pset_analysis = defaultdict(lambda: {'count': 0, 'properties': set(), 'sample_values': {}})

for pset in property_sets:
    pset_name = pset.Name
    pset_analysis[pset_name]['count'] += 1
    
    if hasattr(pset, 'HasProperties'):
        for prop in pset.HasProperties:
            if prop.is_a('IfcPropertySingleValue'):
                prop_name = prop.Name
                pset_analysis[pset_name]['properties'].add(prop_name)
                
                # Store sample value
                if prop_name not in pset_analysis[pset_name]['sample_values']:
                    prop_value = prop.NominalValue.wrappedValue if prop.NominalValue else None
                    pset_analysis[pset_name]['sample_values'][prop_name] = prop_value

# Display property set analysis
print(f"\nProperty Set Summary:")
for pset_name, info in sorted(pset_analysis.items(), key=lambda x: x[1]['count'], reverse=True):
    print(f"\n  {pset_name}: {info['count']} instances")
    print(f"    Properties ({len(info['properties'])}): {', '.join(sorted(info['properties']))}")
    
    if show_detailed_properties and info['sample_values']:
        print(f"    Sample values:")
        for prop_name, value in list(info['sample_values'].items())[:5]:  # Show first 5
            print(f"      {prop_name}: {value}")
        if len(info['sample_values']) > 5:
            print(f"      ... and {len(info['sample_values']) - 5} more properties")

# Analyze spatial containment hierarchy
print("\n=== SPATIAL HIERARCHY ANALYSIS ===")

# Find spatial containment relationships
spatial_contains = ifc_file.by_type('IfcRelContainedInSpatialStructure')
aggregates = ifc_file.by_type('IfcRelAggregates')

print(f"Spatial Containment Relations: {len(spatial_contains)}")
print(f"Aggregation Relations: {len(aggregates)}")

# Build spatial hierarchy
spatial_hierarchy = defaultdict(list)
element_locations = {}

for rel in spatial_contains:
    container = rel.RelatingStructure
    contained_elements = rel.RelatedElements
    
    container_info = f"{container.is_a()}: {getattr(container, 'Name', 'Unnamed')}"
    
    for element in contained_elements:
        element_info = f"{element.is_a()}: {getattr(element, 'Name', 'Unnamed')}"
        spatial_hierarchy[container_info].append(element_info)
        element_locations[element.GlobalId] = container_info

print(f"\nSpatial Hierarchy:")
for container, elements in spatial_hierarchy.items():
    print(f"\n  {container} contains {len(elements)} elements:")
    
    # Group by element type
    element_types = defaultdict(int)
    for element in elements:
        element_type = element.split(':')[0]
        element_types[element_type] += 1
    
    for elem_type, count in sorted(element_types.items()):
        print(f"    {elem_type}: {count}")
    
    # Show sample elements
    if len(elements) <= 10:
        for element in elements:
            print(f"      - {element}")
    else:
        for element in elements[:5]:
            print(f"      - {element}")
        print(f"      ... and {len(elements) - 5} more elements")

# Assess data quality and determine extraction strategy
print("\n=== DATA QUALITY ASSESSMENT ===")

# Test geometry extraction success rates
geometry_stats = {}
coordinate_ranges = {}

for element_type in ['IfcPile', 'IfcBeam', 'IfcColumn', 'IfcSlab', 'IfcWall']:
    elements = ifc_file.by_type(element_type)
    if not elements:
        continue
    
    successful_extractions = 0
    coordinates = []
    
    # Test first 20 elements for geometry extraction
    test_elements = elements[:min(20, len(elements))]
    
    for element in test_elements:
        try:
            if hasattr(element, 'ObjectPlacement') and element.ObjectPlacement:
                settings = ifcopenshell.geom.settings()
                shape = ifcopenshell.geom.create_shape(settings, element)
                if shape:
                    successful_extractions += 1
                    # Extract coordinates
                    matrix = shape.transformation.matrix.data
                    transformation_matrix = np.array(matrix).reshape(4, 4)
                    position = transformation_matrix[:3, 3]
                    coordinates.append(position)
        except Exception:
            continue
    
    success_rate = successful_extractions / len(test_elements) if test_elements else 0
    geometry_stats[element_type] = {
        'total_elements': len(elements),
        'tested_elements': len(test_elements),
        'successful_extractions': successful_extractions,
        'success_rate': success_rate
    }
    
    if coordinates:
        coords_array = np.array(coordinates)
        coordinate_ranges[element_type] = {
            'x_range': [float(coords_array[:, 0].min()), float(coords_array[:, 0].max())],
            'y_range': [float(coords_array[:, 1].min()), float(coords_array[:, 1].max())],
            'z_range': [float(coords_array[:, 2].min()), float(coords_array[:, 2].max())]
        }

print("Geometry Extraction Success Rates:")
for element_type, stats in geometry_stats.items():
    print(f"  {element_type}: {stats['success_rate']:.1%} ({stats['successful_extractions']}/{stats['tested_elements']} tested, {stats['total_elements']} total)")

print("\nCoordinate Ranges by Element Type:")
for element_type, ranges in coordinate_ranges.items():
    print(f"  {element_type}:")
    print(f"    X: {ranges['x_range'][0]:.2f} to {ranges['x_range'][1]:.2f}")
    print(f"    Y: {ranges['y_range'][0]:.2f} to {ranges['y_range'][1]:.2f}")
    print(f"    Z: {ranges['z_range'][0]:.2f} to {ranges['z_range'][1]:.2f}")

# Generate extraction recommendations
print("\n=== EXTRACTION STRATEGY RECOMMENDATIONS ===")

high_success_elements = [elem for elem, stats in geometry_stats.items() if stats['success_rate'] > 0.8]
medium_success_elements = [elem for elem, stats in geometry_stats.items() if 0.5 <= stats['success_rate'] <= 0.8]
low_success_elements = [elem for elem, stats in geometry_stats.items() if stats['success_rate'] < 0.5]

print(f"High Success Rate Elements (>80%): {high_success_elements}")
print(f"Medium Success Rate Elements (50-80%): {medium_success_elements}")
print(f"Low Success Rate Elements (<50%): {low_success_elements}")

print("\nRecommended Extraction Approach:")
print("1. Focus on high-success elements for reliable coordinate extraction")
print("2. Use property sets for additional metadata where geometry fails")
print("3. Implement error handling for geometry extraction")
print("4. Consider coordinate system transformation based on CRS information")
print("5. Export multiple formats (coordinates only, full metadata, element-specific)")

# Save exploration summary
exploration_summary = {
    'file_info': {
        'schema': ifc_file.schema,
        'total_entities': len(list(ifc_file))
    },
    'element_counts': dict(building_elements),
    'geometry_extraction_stats': geometry_stats,
    'coordinate_ranges': coordinate_ranges,
    'property_sets': {name: {'count': info['count'], 'properties': list(info['properties'])} 
                     for name, info in pset_analysis.items()},
    'recommendations': {
        'high_success_elements': high_success_elements,
        'medium_success_elements': medium_success_elements,
        'low_success_elements': low_success_elements
    }
}

summary_path = output_path / f"{Path(ifc_file_path).stem}_exploration_summary.json"
with open(summary_path, 'w') as f:
    json.dump(exploration_summary, f, indent=2)

print(f"\nExploration summary saved: {summary_path}")
print("\nIFC STRUCTURE EXPLORATION COMPLETE")
print("Use insights from this analysis to optimize metadata extraction in notebook 01")

# Create sample DataFrame to demonstrate expected metadata structure
print("\n=== SAMPLE METADATA STRUCTURE PREVIEW ===")
print("This demonstrates the expected pandas DataFrame structure for metadata extraction:")

# Create sample data based on exploration findings
sample_data = []

# Use actual elements if available, otherwise create representative samples
for element_type in ['IfcPile', 'IfcBeam', 'IfcColumn']:
    elements = ifc_file.by_type(element_type)
    if elements:
        # Take first few elements for sample
        for i, element in enumerate(elements[:3]):
            try:
                # Extract basic metadata
                record = {
                    'GlobalId': element.GlobalId,
                    'Name': getattr(element, 'Name', f'{element_type}_{i+1}'),
                    'Type': element.is_a(),
                    'Description': getattr(element, 'Description', None),
                    'Tag': getattr(element, 'Tag', None)
                }
                
                # Try to extract coordinates
                try:
                    if hasattr(element, 'ObjectPlacement') and element.ObjectPlacement:
                        settings = ifcopenshell.geom.settings()
                        shape = ifcopenshell.geom.create_shape(settings, element)
                        if shape:
                            matrix = shape.transformation.matrix.data
                            transformation_matrix = np.array(matrix).reshape(4, 4)
                            position = transformation_matrix[:3, 3]
                            
                            record.update({
                                'X': round(position[0], 3),
                                'Y': round(position[1], 3),
                                'Z': round(position[2], 3),
                                'GeometryExtracted': True
                            })
                        else:
                            record.update({
                                'X': None,
                                'Y': None,
                                'Z': None,
                                'GeometryExtracted': False
                            })
                except Exception:
                    record.update({
                        'X': None,
                        'Y': None,
                        'Z': None,
                        'GeometryExtracted': False
                    })
                
                sample_data.append(record)
                
            except Exception as e:
                print(f"Error processing sample {element_type}: {e}")
                continue

# Create sample DataFrame
if sample_data:
    sample_df = pd.DataFrame(sample_data)
    
    print(f"\nSample metadata structure (df.head() style):")
    print(f"Shape: {sample_df.shape}")
    print(f"Columns: {list(sample_df.columns)}")
    
    # Display sample data
    print(f"\nSample data preview:")
    display_cols = ['Name', 'Type', 'X', 'Y', 'Z', 'GeometryExtracted']
    available_cols = [col for col in display_cols if col in sample_df.columns]
    
    preview_df = sample_df[available_cols]
    print(preview_df.to_string(index=False, float_format='%.3f'))
    
    # Show data types
    print(f"\nData types:")
    for col in sample_df.columns:
        print(f"  {col}: {sample_df[col].dtype}")
    
    # Show completeness
    print(f"\nData completeness:")
    for col in sample_df.columns:
        non_null_count = sample_df[col].notna().sum()
        total_count = len(sample_df)
        completeness = non_null_count / total_count * 100
        print(f"  {col}: {non_null_count}/{total_count} ({completeness:.1f}%)")
    
    print(f"\nThis structure will be used in notebook 01 for systematic metadata extraction.")
    
else:
    print("No sample data could be extracted for preview")
    print("Expected DataFrame structure:")
    print("Columns: ['GlobalId', 'Name', 'Type', 'Description', 'Tag', 'X', 'Y', 'Z', 'GeometryExtracted']")
    print("This structure will guide the implementation in notebook 01")