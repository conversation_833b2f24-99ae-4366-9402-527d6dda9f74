# Parameters
ifc_file_path = "/Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/raw/trino_enel/ifc/GRE.EEC.S.00.IT.P.14353.00.265.ifc"
output_dir = "../../data/processed/trino_enel/ifc_metadata"
site_name = "trino_enel"

# Processing parameters
target_crs = "EPSG:32632"  # UTM Zone 32N for Italy
priority_elements = ["IfcPile", "IfcBeam", "IfcColumn", "IfcSlab", "IfcWall"]
pile_name_keywords = ["Pile", "pile", "PILE", "Foundation", "FOUNDATION"]  # Keywords for pile identification
extract_properties = True
extract_materials = True
extract_geolocation = True
coordinate_precision = 6  # Higher precision for geographic coordinates

# Import all required packages
import ifcopenshell
import ifcopenshell.geom
import numpy as np
import pandas as pd
from pyproj import Transformer, CRS
import json
import logging
from typing import Dict, List, Optional, Tuple
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')
    
print("All packages imported successfully")
    
# Setup
logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

output_path = Path(output_dir)
output_path.mkdir(parents=True, exist_ok=True)

print("\nENHANCED IFC METADATA EXTRACTION (FIXED)")
print(f"Output directory: {output_path}")

print("Dependencies loaded:")
print(f"  ifcopenshell: {ifcopenshell.__version__}")
print(f"  numpy: {np.__version__}")
print(f"  pandas: {pd.__version__}")
print(f"  pyproj: {pyproj.__version__}")


from pathlib import Path
import ifcopenshell

def load_ifc_file(ifc_file_path: str) -> ifcopenshell.file:
    print("=== IFC FILE VALIDATION ===")
    ifc_path = Path(ifc_file_path)

    if not ifc_path.is_file():
        raise FileNotFoundError(f"IFC file not found: {ifc_path}")

    print(f"File: {ifc_path.name}")
    print(f"Size: {ifc_path.stat().st_size / 1024 ** 2:.1f} MB")

    try:
        ifc_file = ifcopenshell.open(str(ifc_path))
        print("IFC file loaded successfully")
        print(f"Schema: {ifc_file.schema}")
        print(f"Entities: {len(list(ifc_file)):,}")
        return ifc_file
    except Exception as e:
        raise RuntimeError(f"Failed to load IFC file: {e}")
    
load_ifc_file(ifc_file_path)

def safe_column_check(df, columns):
    """Safely check if columns exist in DataFrame."""
    if isinstance(columns, str):
        columns = [columns]
    return all(col in df.columns for col in columns)

def safe_column_access(df, columns, default_value=None):
    """Safely access DataFrame columns, return default if missing."""
    if isinstance(columns, str):
        columns = [columns]
    
    available_cols = [col for col in columns if col in df.columns]
    
    if not available_cols:
        return pd.Series([default_value] * len(df))
    
    if len(available_cols) == 1:
        return df[available_cols[0]]
    else:
        return df[available_cols]

def extract_ifc_site_geolocation(ifc_file):
    """Extract site geolocation from IFC file."""
    sites = ifc_file.by_type("IfcSite")
    if not sites:
        return None, None, None
    
    site = sites[0]
    lat = getattr(site, "RefLatitude", None)
    lon = getattr(site, "RefLongitude", None)
    elev = getattr(site, "RefElevation", None)
    return lat, lon, elev

def convert_ifc_angle_to_decimal_degrees(ifc_angle):
    """Convert IFC angle format to decimal degrees."""
    if ifc_angle is None:
        return None
    
    if isinstance(ifc_angle, (list, tuple)) and len(ifc_angle) >= 3:
        degrees = ifc_angle[0] if ifc_angle[0] is not None else 0
        minutes = ifc_angle[1] if ifc_angle[1] is not None else 0
        seconds = ifc_angle[2] if ifc_angle[2] is not None else 0
        microseconds = ifc_angle[3] if len(ifc_angle) > 3 and ifc_angle[3] is not None else 0
        
        decimal_degrees = degrees + minutes/60.0 + seconds/3600.0 + microseconds/3600000000.0
        return decimal_degrees
    
    if isinstance(ifc_angle, (int, float)):
        return float(ifc_angle)
    
    return None

print("Helper functions ready")

# Extract site geolocation
print("\n=== SITE GEOLOCATION EXTRACTION ===")

lat_raw, lon_raw, elevation = extract_ifc_site_geolocation(ifc_file)

if lat_raw is not None or lon_raw is not None:
    print(f"Raw site data found:")
    print(f"  RefLatitude: {lat_raw}")
    print(f"  RefLongitude: {lon_raw}")
    print(f"  RefElevation: {elevation}")
    
    # Convert to decimal degrees
    lat_dd = convert_ifc_angle_to_decimal_degrees(lat_raw)
    lon_dd = convert_ifc_angle_to_decimal_degrees(lon_raw)
    
    print(f"\nConverted to decimal degrees:")
    print(f"  Latitude: {lat_dd:.6f}°" if lat_dd is not None else "  Latitude: Not available")
    print(f"  Longitude: {lon_dd:.6f}°" if lon_dd is not None else "  Longitude: Not available")
    print(f"  Elevation: {elevation:.3f}m" if elevation is not None else "  Elevation: Not available")
    
    site_geolocation = {
        'latitude': lat_dd,
        'longitude': lon_dd,
        'elevation': elevation
    }
else:
    print("No site geolocation data found in IFC file")
    site_geolocation = {
        'latitude': None,
        'longitude': None,
        'elevation': None
    }

print(f"\nSite geolocation extraction complete")

import ifcopenshell
import ifcopenshell.geom
import numpy as np
import pandas as pd
from pyproj import Transformer


def count_priority_elements(ifc_file, priority_elements):
    element_counts = {}
    total = 0
    for elem_type in priority_elements:
        elements = ifc_file.by_type(elem_type)
        count = len(elements)
        element_counts[elem_type] = count
        total += count
        print(f"{elem_type}: {count} elements")
    print(f"\nTotal elements to process: {total}")
    return element_counts, total


def extract_element_metadata(element, settings, site_geolocation, extract_geolocation, precision, logger=None):
    record = {
        'GlobalId': element.GlobalId,
        'Name': getattr(element, 'Name', None),
        'Type': element.is_a(),
        'Description': getattr(element, 'Description', None),
        'Tag': getattr(element, 'Tag', None)
    }

    if extract_geolocation and site_geolocation:
        record.update({
            'Site_Latitude': site_geolocation.get('latitude'),
            'Site_Longitude': site_geolocation.get('longitude'),
            'Site_Elevation': site_geolocation.get('elevation')
        })

    try:
        if hasattr(element, 'ObjectPlacement') and element.ObjectPlacement:
            shape = ifcopenshell.geom.create_shape(settings, element)
            verts = np.array(shape.geometry.verts).reshape(-1, 3)
            centroid = verts.mean(axis=0)
            x, y, z = centroid
            record.update({
                'X': round(x, precision),
                'Y': round(y, precision),
                'Z': round(z, precision),
                'GeometryExtracted': True
            })
        else:
            record.update({'X': None, 'Y': None, 'Z': None, 'GeometryExtracted': False})
    except Exception as e:
        if logger:
            logger.warning(f"Geometry error for {element.GlobalId}: {e}")
        record.update({'X': None, 'Y': None, 'Z': None, 'GeometryExtracted': False})

    return record


def detect_piles_by_name(metadata_df, pile_keywords):
    mask = metadata_df['Name'].str.contains('|'.join(pile_keywords), case=False, na=False)
    pile_df = metadata_df[mask]
    print(f"Piles identified by name: {len(pile_df)}")
    if not pile_df.empty:
        print("Sample pile names:")
        print(pile_df['Name'].dropna().head(3).to_string(index=False))
        valid_coords = pile_df[['X', 'Y', 'Z']].notna().all(axis=1).sum()
        print(f"Pile entries with valid coordinates: {valid_coords}")
    return pile_df


def transform_coordinates(metadata_df, target_crs, precision):
    if {'X', 'Y'}.issubset(metadata_df.columns) and metadata_df[['X', 'Y']].notna().any().any():
        print("Attempting coordinate transformation to WGS84...")
        transformer = Transformer.from_crs(target_crs, "EPSG:4326", always_xy=True)
        lons, lats = [], []
        for _, row in metadata_df.iterrows():
            x, y = row['X'], row['Y']
            if pd.notna(x) and pd.notna(y):
                try:
                    lon, lat = transformer.transform(x, y)
                    lons.append(round(lon, precision))
                    lats.append(round(lat, precision))
                except:
                    lons.append(None)
                    lats.append(None)
            else:
                lons.append(None)
                lats.append(None)
        metadata_df['Longitude'] = lons
        metadata_df['Latitude'] = lats
        print(f"Transformed {metadata_df['Longitude'].notna().sum()} of {len(metadata_df)} coordinates to WGS84")
    else:
        print("No valid X, Y coordinates available for transformation")
    return metadata_df


def extract_and_enrich_ifc_metadata(
    ifc_file_path,
    priority_elements,
    site_geolocation=None,
    coordinate_precision=3,
    extract_geolocation=True,
    pile_name_keywords=None,
    target_crs="EPSG:32643",
    logger=None
):
    print("=== MAIN METADATA EXTRACTION ===")
    ifc_file = ifcopenshell.open(ifc_file_path)
    settings = ifcopenshell.geom.settings()
    settings.set(settings.USE_WORLD_COORDS, True)

    element_counts, total = count_priority_elements(ifc_file, priority_elements)
    if total == 0:
        print("No priority elements found")
        return pd.DataFrame()

    records = []
    for element_type in priority_elements:
        elements = ifc_file.by_type(element_type)
        print(f"\nProcessing {len(elements)} {element_type} elements...")
        for i, element in enumerate(elements):
            try:
                record = extract_element_metadata(
                    element, settings, site_geolocation,
                    extract_geolocation, coordinate_precision, logger
                )
                records.append(record)
                if (i + 1) % 100 == 0:
                    print(f"Processed {i+1}/{len(elements)} {element_type} elements")
            except Exception as e:
                if logger:
                    logger.error(f"Error processing {element_type} {element.GlobalId}: {e}")
                continue

    metadata_df = pd.DataFrame(records)
    print(f"\nMetadata extracted: {len(metadata_df)} records")

    if pile_name_keywords:
        detect_piles_by_name(metadata_df, pile_name_keywords)

    metadata_df = transform_coordinates(metadata_df, target_crs, coordinate_precision)

    return metadata_df

metadata_df = extract_and_enrich_ifc_metadata(
    ifc_file_path=ifc_file_path,
    priority_elements=["IfcColumn", "IfcFooting"],
    site_geolocation={"latitude": 42.414864, "longitude": -71.258072, "elevation": 0.0},
    coordinate_precision=3,
    extract_geolocation=True,
    pile_name_keywords=["pile", "PILE", "TRPL"],
    target_crs="EPSG:32632"
)
metadata_df.head()

import pandas as pd

def analyze_metadata_quality(metadata_df):
    if len(metadata_df) == 0:
        print("No data available for analysis")
        return

    print("\n=== METADATA QUALITY ANALYSIS ===")

    # Type distribution
    if 'Type' in metadata_df.columns:
        type_distribution = metadata_df['Type'].value_counts()
        print("Element Type Distribution:")
        for elem_type, count in type_distribution.items():
            print(f"  {elem_type}: {count:,}")

    # Coordinate completeness (assumes X/Y/Z exist)
    print("\nCoordinate Data Quality:")

    local_complete = metadata_df[['X', 'Y', 'Z']].notna().all(axis=1)
    print(f"  Local (X, Y, Z): {local_complete.sum():,}/{len(metadata_df):,} ({local_complete.mean() * 100:.1f}%)")

    coords_df = metadata_df[local_complete]
    if not coords_df.empty:
        print(f"    X range: {coords_df['X'].min():.3f} to {coords_df['X'].max():.3f}")
        print(f"    Y range: {coords_df['Y'].min():.3f} to {coords_df['Y'].max():.3f}")
        print(f"    Z range: {coords_df['Z'].min():.3f} to {coords_df['Z'].max():.3f}")

    # Geographic coordinates
    if {'Latitude', 'Longitude'}.issubset(metadata_df.columns):
        geo_complete = metadata_df[['Latitude', 'Longitude']].notna().all(axis=1)
        print(f"  Geographic (Lat, Lon): {geo_complete.sum():,}/{len(metadata_df):,} ({geo_complete.mean() * 100:.1f}%)")

        geo_df = metadata_df[geo_complete]
        if not geo_df.empty:
            print(f"    Latitude range: {geo_df['Latitude'].min():.6f} to {geo_df['Latitude'].max():.6f}")
            print(f"    Longitude range: {geo_df['Longitude'].min():.6f} to {geo_df['Longitude'].max():.6f}")
    else:
        print("  Geographic coordinates not available")

    # Site geolocation based on median of transformed coordinates
    if {'Latitude', 'Longitude'}.issubset(metadata_df.columns):
        site_lat = metadata_df['Latitude'].median()
        site_lon = metadata_df['Longitude'].median()
        metadata_df['Site_Latitude'] = site_lat
        metadata_df['Site_Longitude'] = site_lon
        print(f"  Site geolocation fixed to median: ({site_lat:.6f}, {site_lon:.6f})")
    else:
        print("  Site geolocation could not be fixed — missing transformed coordinates")



    pd.set_option('display.max_columns', None)
    pd.set_option('display.width', None)
    pd.set_option('display.max_colwidth', 25)

    print("\nFirst 10 records:")
    print(metadata_df[preview_cols].head(10).to_string(index=False, float_format='%.6f'))

    print(f"\nDataFrame Info:")
    print(f"  Shape: {metadata_df.shape}")
    print(f"  Columns: {list(metadata_df.columns)}")


analyze_metadata_quality(metadata_df)


import pandas as pd
import json
from pathlib import Path

def export_ifc_metadata(
    metadata_df: pd.DataFrame,
    output_path: Path,
    ifc_file_path: str,
    priority_elements: list,
    target_crs: str,
    site_geolocation: dict = None,
    ifc_file=None
):
    if len(metadata_df) == 0:
        print("No metadata to export")
        return

    output_path.mkdir(parents=True, exist_ok=True)
    ifc_filename = Path(ifc_file_path).stem
    file_prefix = output_path / ifc_filename

    print("\nExporting IFC metadata:")

    # 1. Save complete metadata
    full_path = file_prefix.with_name(file_prefix.name + "_enhanced_metadata.csv")
    metadata_df.to_csv(full_path, index=False)
    print(f"- Metadata CSV: {full_path}")

    # 2. Save coordinates (local + WGS84)
    coord_cols = ['GlobalId', 'Name', 'Type', 'X', 'Y', 'Z', 'Latitude', 'Longitude']
    coord_df = metadata_df[[c for c in coord_cols if c in metadata_df.columns]].dropna(subset=['X', 'Y', 'Z'])
    if not coord_df.empty:
        coord_path = file_prefix.with_name(file_prefix.name + "_coordinates.csv")
        coord_df.to_csv(coord_path, index=False)
        print(f"- Coordinates CSV: {coord_path}")

    # 3. Save piles if any
    if 'Name' in metadata_df.columns:
        pile_mask = metadata_df['Name'].str.contains('pile|PILE|TRPL', case=False, na=False)
        pile_df = metadata_df[pile_mask & metadata_df[['X', 'Y', 'Z']].notna().all(axis=1)]
        if not pile_df.empty:
            pile_path = file_prefix.with_name(file_prefix.name + "_piles.csv")
            pile_df.to_csv(pile_path, index=False)
            print(f"- Pile CSV: {pile_path}")

    # 4. Write summary JSON
    summary = {
        "ifc_file": Path(ifc_file_path).name,
        "schema": getattr(ifc_file, "schema", "Unknown"),
        "elements": len(metadata_df),
        "priority_types": priority_elements,
        "target_crs": target_crs,
        "site_geolocation": site_geolocation or {},
        "type_distribution": metadata_df['Type'].value_counts().to_dict() if 'Type' in metadata_df.columns else {},
        "columns": list(metadata_df.columns)
    }
    json_path = file_prefix.with_name(file_prefix.name + "_summary.json")
    with open(json_path, "w") as f:
        json.dump(summary, f, indent=2)
    print(f"- Summary JSON: {json_path}")

export_ifc_metadata(
    metadata_df=metadata_df,
    output_path=output_path,
    ifc_file_path="models/tracker_site.ifc",
    priority_elements=["IfcColumn", "IfcFooting"],
    target_crs="EPSG:32632",
    site_geolocation={"latitude": 45.26, "longitude": 8.18, "elevation": 0.0},
    ifc_file=ifc_file  # optional
)
